"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle2, ExternalLink, ArrowLeft } from "lucide-react";
import Link from "next/link";

interface ConnectionData {
  connection: {
    name: string;
    platform: string;
    platformAccountName?: string;
  };
  project: {
    name: string;
  };
  linkName: string;
}

export default function PublicConnectionSuccessPage() {
  const params = useParams();
  const token = params.token as string;
  const [connectionData, setConnectionData] = useState<ConnectionData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchConnectionData = async () => {
      try {
        // Get connection details for the success message
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/public/link/${token}/validate`
        );

        if (response.ok) {
          const data = await response.json();
          setConnectionData(data);
        }
      } catch (error) {
        console.error("Failed to fetch connection data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      fetchConnectionData();
    }
  }, [token]);

  const getPlatformDisplayName = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "YouTube";
      case "instagram":
        return "Instagram Personal";
      case "instagram_business":
        return "Instagram Business";
      case "instagram_with_facebook":
        return "Instagram with Facebook";
      case "facebook":
        return "Facebook";
      case "tiktok":
        return "TikTok";
      default:
        return platform;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "bg-red-500";
      case "instagram":
      case "instagram_business":
      case "instagram_with_facebook":
        return "bg-gradient-to-r from-purple-500 to-pink-500";
      case "facebook":
        return "bg-blue-600";
      case "tiktok":
        return "bg-black";
      default:
        return "bg-gray-500";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading connection details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto py-8">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle2 className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold mb-2">Connection Successful!</h1>
          <p className="text-muted-foreground">
            Your account has been successfully connected.
          </p>
        </div>

        {connectionData && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div
                  className={`w-8 h-8 rounded-lg ${getPlatformColor(connectionData.connection.platform)} flex items-center justify-center text-white font-semibold text-sm`}
                >
                  {getPlatformDisplayName(
                    connectionData.connection.platform
                  ).charAt(0)}
                </div>
                {connectionData.linkName}
              </CardTitle>
              <CardDescription>
                Project: {connectionData.project.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                    <div>
                      <h3 className="font-medium text-green-900">
                        {getPlatformDisplayName(
                          connectionData.connection.platform
                        )}{" "}
                        Connected
                      </h3>
                      <p className="text-sm text-green-700">
                        {connectionData.connection.platformAccountName
                          ? `Connected as ${connectionData.connection.platformAccountName}`
                          : "Your account is now connected and ready to use"}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  <h4 className="font-medium mb-2">What happens next?</h4>
                  <ul className="space-y-1">
                    <li>• Your account is now connected to the project</li>
                    <li>
                      • The project team can now access your content as
                      configured
                    </li>
                    <li>
                      • You can disconnect your account at any time if needed
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            You can now close this window or return to the connection page.
          </p>

          <div className="flex justify-center gap-3">
            <Button
              variant="outline"
              onClick={() => window.close()}
              className="flex items-center gap-2"
            >
              Close Window
            </Button>

            <Link href={`/connect/${token}`}>
              <Button variant="outline" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Connection
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-8 text-center text-xs text-muted-foreground">
          <p>
            This connection is secure and managed by the project team.
            <br />
            If you have any questions, please contact the person who shared this
            link.
          </p>
        </div>
      </div>
    </div>
  );
}
