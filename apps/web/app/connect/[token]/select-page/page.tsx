import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import { FacebookPageSelectorPublic } from "@/components/facebook/FacebookPageSelectorPublic";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";

interface PageProps {
  params: {
    token: string;
  };
}

async function validateLinkAndGetConnection(token: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/public/link/validate?token=${token}`,
      {
        method: "GET",
      }
    );

    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    if (!data.isValid || data.isExpired) {
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error validating link:", error);
    return null;
  }
}

export default async function SelectFacebookPagePublicPage({
  params,
}: PageProps) {
  const linkData = await validateLinkAndGetConnection(params.token);

  if (!linkData) {
    notFound();
  }

  const { connection, project } = linkData;

  // Verify this is a Facebook connection that needs page selection
  if (connection.platform !== "facebook") {
    redirect(`/connect/${params.token}/success`);
  }

  if (!connection.pendingPageSelection) {
    // Already has a page selected, redirect to success page
    redirect(`/connect/${params.token}/success`);
  }

  const handlePageSelected = () => {
    // Redirect to success page after successful page selection
    window.location.href = `/connect/${params.token}/success`;
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Complete Facebook Connection</h1>
          <p className="text-muted-foreground mt-2">
            Your Facebook account has been successfully authenticated for{" "}
            <strong>{project.name}</strong>. Now select which Facebook Page you
            want to connect.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Facebook Page Selection</CardTitle>
            <CardDescription>
              Choose the Facebook Page whose content you want to synchronize
              with {project.name}.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-blue-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-blue-800">
                    Important: Business Portfolio Limitations
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Some Facebook Pages may not appear in this list if they
                      are managed by a Meta Business Portfolio that we don't
                      have permissions to access. If you don't see a page you
                      expect to manage:
                    </p>
                    <ul className="mt-2 list-disc list-inside space-y-1">
                      <li>Ensure you have admin access to the Facebook Page</li>
                      <li>
                        Check if the page is managed through a Business
                        Portfolio
                      </li>
                      <li>
                        If using Business Portfolio, make sure our app has the
                        necessary permissions
                      </li>
                      <li>
                        Try reconnecting your Facebook account if the page was
                        recently added
                      </li>
                    </ul>
                    <div className="mt-4 pt-4 border-t border-blue-200">
                      <p className="text-sm text-blue-800 mb-3">
                        If you're still missing pages, you can reconnect your
                        Facebook account to refresh permissions:
                      </p>
                      <a
                        href={`/connect/${params.token}`}
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                          />
                        </svg>
                        Reconnect Facebook Account
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Suspense
              fallback={
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading...</span>
                </div>
              }
            >
              <FacebookPageSelectorPublic
                projectId={connection.projectId}
                connectionId={connection.id}
                token={params.token}
                onPageSelected={handlePageSelected}
              />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
