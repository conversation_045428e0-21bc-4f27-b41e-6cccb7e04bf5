import type React from "react";
import Link from "next/link";
import { Zap } from "lucide-react";

export default function ImprintPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Zap className="size-4" />
              </div>
              <span className="text-xl font-bold">Social Feed API</span>
            </Link>
          </div>
        </div>
      </nav>

      {/* Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Imprint</h1>
          
          <div className="prose prose-lg max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Information according to § 5 TMG</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="mb-2"><strong>Company Name:</strong> [Your Company Name]</p>
                <p className="mb-2"><strong>Address:</strong> [Your Street Address]</p>
                <p className="mb-2"><strong>City:</strong> [Your City, Postal Code]</p>
                <p className="mb-2"><strong>Country:</strong> [Your Country]</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Contact Information</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="mb-2"><strong>Phone:</strong> [Your Phone Number]</p>
                <p className="mb-2"><strong>Email:</strong> [Your Email Address]</p>
                <p className="mb-2"><strong>Website:</strong> [Your Website URL]</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Legal Information</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="mb-2"><strong>Managing Director:</strong> [Name of Managing Director]</p>
                <p className="mb-2"><strong>Commercial Register:</strong> [Register Court and Number]</p>
                <p className="mb-2"><strong>VAT ID:</strong> [Your VAT Identification Number]</p>
                <p className="mb-2"><strong>Tax Number:</strong> [Your Tax Number]</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Responsible for Content</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="mb-2">According to § 55 Abs. 2 RStV:</p>
                <p className="mb-2"><strong>Name:</strong> [Name of Responsible Person]</p>
                <p className="mb-2"><strong>Address:</strong> [Address of Responsible Person]</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Disclaimer</h2>
              
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Liability for Content</h3>
              <p className="mb-4 text-gray-700">
                As service providers, we are liable for own contents of these websites according to Sec. 7, 
                paragraph 1 German Telemedia Act (TMG). However, according to Sec. 8 to 10 German Telemedia Act (TMG), 
                service providers are not under obligation to permanently monitor submitted or stored information 
                or to search for evidences that indicate illegal activities.
              </p>
              <p className="mb-4 text-gray-700">
                Legal obligations to removing information or to blocking the use of information remain unchallenged. 
                In this case, liability is only possible at the time of knowledge about a specific violation of law. 
                Illegal contents will be removed immediately at the time we get knowledge of them.
              </p>

              <h3 className="text-xl font-semibold text-gray-800 mb-3">Liability for Links</h3>
              <p className="mb-4 text-gray-700">
                Our offer includes links to external third party websites. We have no influence on the contents 
                of those websites, therefore we cannot guarantee for those contents. Providers or administrators 
                of linked websites are always responsible for their own contents.
              </p>
              <p className="mb-4 text-gray-700">
                The linked websites had been checked for possible violations of law at the time of the establishment 
                of the link. Illegal contents were not detected at the time of the linking. A permanent monitoring 
                of the contents of linked websites cannot be imposed without reasonable indications that there has 
                been a violation of law. Illegal links will be removed immediately at the time we get knowledge of them.
              </p>

              <h3 className="text-xl font-semibold text-gray-800 mb-3">Copyright</h3>
              <p className="mb-4 text-gray-700">
                Contents and compilations published on these websites by the providers are subject to German copyright laws. 
                Reproduction, editing, distribution as well as the use of any kind outside the scope of the copyright law 
                require a written permission of the author or originator. Downloads and copies of these websites are permitted 
                for private use only.
              </p>
              <p className="mb-4 text-gray-700">
                The commercial use of our contents without permission of the originator is prohibited.
              </p>
              <p className="mb-4 text-gray-700">
                Copyright laws of third parties are respected as long as the contents on these websites do not originate 
                from the provider. Contributions of third parties on this site are indicated as such. However, if you notice 
                any violations of copyright law, please inform us. Such contents will be removed immediately.
              </p>
            </section>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-4">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Zap className="size-4" />
              </div>
              <span className="text-xl font-bold">Social Feed API</span>
            </Link>
            <div className="flex justify-center space-x-6 text-gray-400">
              <Link href="/" className="hover:text-white">
                Home
              </Link>
              <Link href="/imprint" className="hover:text-white">
                Imprint
              </Link>
              <Link href="/privacy" className="hover:text-white">
                Privacy Policy
              </Link>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Social API. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
