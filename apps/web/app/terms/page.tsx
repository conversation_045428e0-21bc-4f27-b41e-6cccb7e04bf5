import type React from "react";
import Link from "next/link";
import { Zap } from "lucide-react";

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Zap className="size-4" />
              </div>
              <span className="text-xl font-bold">Social Feed API</span>
            </Link>
          </div>
        </div>
      </nav>

      {/* Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Terms of Service</h1>

          <div className="prose prose-lg max-w-none">
            <section className="mb-8">
              <p className="text-gray-700 mb-4">
                <strong>Last updated:</strong> {new Date().toLocaleDateString()}
              </p>
              <p className="text-gray-700 mb-4">
                These Terms of Service ("Terms") govern your access to and use of Social Feed API ("we", "us", or "our").
                By accessing or using the Service, you agree to be bound by these Terms.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700">
                By using the Service, you confirm that you have read, understood, and agree to these Terms and our Privacy Policy.
                If you do not agree, you may not use the Service.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Eligibility</h2>
              <p className="text-gray-700">
                You must be at least 18 years old and have the legal capacity to enter into a binding agreement to use the Service.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. Accounts</h2>
              <p className="text-gray-700 mb-4">
                You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
                You must promptly notify us of any unauthorized use of your account.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Subscriptions and Billing</h2>
              <p className="text-gray-700 mb-4">
                Certain features may require a paid subscription. Fees, billing terms, and plan limits are described at the time of purchase.
                Unless otherwise stated, subscriptions renew automatically until canceled.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Acceptable Use</h2>
              <ul className="list-disc pl-6 mb-4 text-gray-700">
                <li>Do not use the Service for unlawful purposes or in violation of any applicable law or regulation.</li>
                <li>Do not attempt to access data you are not authorized to access.</li>
                <li>Do not interfere with or disrupt the integrity or performance of the Service.</li>
                <li>Respect third-party platform terms when using integrations (e.g., Meta, YouTube, etc.).</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Intellectual Property</h2>
              <p className="text-gray-700 mb-4">
                We retain all rights, title, and interest in and to the Service, including all related intellectual property.
                You are granted a limited, non-exclusive, non-transferable license to use the Service in accordance with these Terms.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. API Usage and Rate Limits</h2>
              <p className="text-gray-700 mb-4">
                Your use of the API is subject to rate limits and fair use policies defined by your plan. We may monitor usage to ensure compliance and may throttle or suspend access in cases of abuse.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Third-Party Services</h2>
              <p className="text-gray-700 mb-4">
                The Service integrates with third-party platforms. We are not responsible for third-party services and do not control their terms or policies.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Termination</h2>
              <p className="text-gray-700 mb-4">
                We may suspend or terminate your access to the Service at any time for any violation of these Terms.
                You may stop using the Service at any time; upon termination, your right to access the Service will cease immediately.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">10. Disclaimers</h2>
              <p className="text-gray-700 mb-4">
                The Service is provided on an "AS IS" and "AS AVAILABLE" basis without warranties of any kind, whether express or implied.
                We do not warrant that the Service will be uninterrupted, error-free, or secure.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">11. Limitation of Liability</h2>
              <p className="text-gray-700 mb-4">
                To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages,
                or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">12. Indemnification</h2>
              <p className="text-gray-700 mb-4">
                You agree to defend, indemnify, and hold harmless us and our affiliates from any claims, liabilities, damages, losses, and expenses
                arising out of or in any way connected with your use of the Service or violation of these Terms.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">13. Changes to the Terms</h2>
              <p className="text-gray-700 mb-4">
                We may update these Terms from time to time. If we make material changes, we will notify you by posting the updated Terms on this page
                and updating the "Last updated" date above.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">14. Governing Law</h2>
              <p className="text-gray-700 mb-4">
                These Terms shall be governed by and construed in accordance with the laws of [Your Jurisdiction], without regard to its conflict of law principles.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">15. Contact</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="mb-2 text-gray-700">
                  For questions about these Terms, please contact us:
                </p>
                <p className="mb-2"><strong>Email:</strong> [Your Legal Contact Email]</p>
                <p className="mb-2"><strong>Address:</strong> [Your Company Address]</p>
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Link href="/" className="inline-flex items-center space-x-2 mb-4">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Zap className="size-4" />
              </div>
              <span className="text-xl font-bold">Social Feed API</span>
            </Link>
            <div className="flex justify-center space-x-6 text-gray-400">
              <Link href="/" className="hover:text-white">
                Home
              </Link>
              <Link href="/imprint" className="hover:text-white">
                Imprint
              </Link>
              <Link href="/privacy" className="hover:text-white">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-white">
                Terms of Service
              </Link>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Social API. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
