import type React from "react";
import Link from "next/link";
import { ArrowLeft, ChevronLeft } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// This component renders the top bar
export async function ProjectHeaderContent({
  projectId,
  name,
}: {
  projectId: string;
  name: string;
}) {
  return (
    <div className="flex h-16 items-center px-4 md:px-6">
      <div className="flex flex-1 items-center justify-between space-x-2 md:space-x-4">
        <div className="flex space-x-9 items-center">
          <Link href="/dashboard/projects">
            <Button variant="ghost" size="icon" className="md:hidden">
              <ArrowLeft className="h-5 w-5" />
              <span className="sr-only">Back</span>
            </Button>
            <Button variant="default" size="icon" className="rounded-full">
              <ChevronLeft className="!size-6" />
            </Button>
          </Link>
          <h1
            className="text-base md:text-lg font-semibold truncate"
            title={name || "Page"}
          >
            {name || "Page"} {/* Fallback page title */}
          </h1>
        </div>
        <Badge variant="outline" className="text-xs whitespace-nowrap">
          ID: {projectId}
        </Badge>
      </div>
    </div>
  );
}
