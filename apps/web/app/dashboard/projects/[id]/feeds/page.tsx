import Link from "next/link";
import {
  Plus,
  <PERSON>ting<PERSON>,
  Key,
  CheckCircle,
  XCircle,
  AlertCircle,
  Rss,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import CreateFeedButton from "./_components/CreateFeedButton";
import { getFeeds } from "@/lib/fetch/getFeeds";
import { getConnections } from "@/lib/fetch/getConnections";
import StatusBadge from "@/components/primitives/StatusBadge";
import StatusIcon from "@/components/primitives/StatusIcon";

export default async function FeedsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const projectId = (await params).id as string;

  const feeds = await getFeeds(projectId);
  const connections = await getConnections(projectId);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "inactive":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = { active: "default", paused: "secondary" } as const;
    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Data Feeds</CardTitle>
              <CardDescription>
                Manage feeds that aggregate data from your connections. Each
                feed has its own API settings.
              </CardDescription>
            </div>
            <CreateFeedButton connections={connections} projectId={projectId} />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Feed</TableHead>
                <TableHead>Connections</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Posts</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {feeds.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="justify-items-center">
                    <p className="text-sm py-2 text-muted-foreground">
                      You have no feeds yet.
                    </p>
                  </TableCell>
                </TableRow>
              )}
              {feeds.map((feed) => {
                const connectionCount = feed.connections.length;
                const connectionActiveCount = feed.connections.filter(
                  (c) => c.status === "active"
                ).length;
                return (
                  <TableRow key={feed.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Rss className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{feed.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {feed.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {connectionCount}
                        {connectionActiveCount < connectionCount && (
                          <span className="text-muted-foreground">
                            {" "}
                            ({connectionActiveCount} working)
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <StatusIcon
                          status={
                            connectionActiveCount < connectionCount
                              ? "error"
                              : "active"
                          }
                        />
                        <StatusBadge
                          status={
                            connectionActiveCount < connectionCount
                              ? "action needed"
                              : "active"
                          }
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{100}</div>
                    </TableCell>
                    <TableCell>{feed.lastActivity ?? "never"}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/dashboard/projects/${projectId}/feeds/${feed.id}`}
                        >
                          <Button size="sm" variant="outline">
                            <Settings className="h-4 w-4 mr-1" />
                            Configure
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
