import { getFeed } from "@/lib/fetch/getFeed";
import { getConnections } from "@/lib/fetch/getConnections";
import DeleteFeedSettings from "./_components/DeleteFeedSettings";
import ApiKeysFeedSettings from "./_components/ApiKeysFeedSettings";
import ConnectionFeedSettings from "./_components/ConnectionFeedSettings";
import BasicFeedSettings from "./_components/BasicFeedSettings";

export default async function FeedConfigurationPage({
  params,
}: {
  params: Promise<{ id: string; feedId: string }>;
}) {
  const { id: projectId, feedId } = await params;

  const feedData = await getFeed(projectId, feedId);
  const connections = await getConnections(projectId);

  // const [isCreateWebhookOpen, setIsCreateWebhookOpen] = useState(false);
  // const [newWebhookUrl, setNewWebhookUrl] = useState("");
  // const [newWebhookEvents, setNewWebhookEvents] = useState<string[]>([]);

  /*

  const handleCreateWebhook = () => {
    toast({
      title: "Webhook created",
      description: "New webhook endpoint has been configured successfully.",
    });
    setIsCreateWebhookOpen(false);
    setNewWebhookUrl("");
    setNewWebhookEvents([]);
  };

  */

  return (
    <div className="space-y-6">
      {/* Max-width can be applied here or in the layout if needed */}
      {/* Basic Settings */}
      <BasicFeedSettings
        name={feedData.name}
        description={feedData.description ?? undefined}
        isActive={feedData.isActive}
        projectId={projectId}
        feedId={feedId}
      />
      {/* Connection Selection */}
      <ConnectionFeedSettings
        feedConnections={feedData.connections}
        availableConnections={connections}
        projectId={projectId}
        feedId={feedId}
      />
      {/* API Keys Management */}
      <ApiKeysFeedSettings
        projectId={projectId}
        feedId={feedId}
        apiKeys={feedData.apiKeys}
      />
      {/* Webhooks */}
      {/*}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Webhook className="h-5 w-5" />
                <span>Feed Webhooks</span>
              </CardTitle>
              <CardDescription>
                Configure webhook endpoints to receive notifications about this
                feed.
              </CardDescription>
            </div>
            <Dialog
              open={isCreateWebhookOpen}
              onOpenChange={setIsCreateWebhookOpen}
            >
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Webhook
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Feed Webhook Endpoint</DialogTitle>
                  <DialogDescription>
                    Configure a new webhook endpoint to receive feed
                    notifications.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://your-app.com/webhooks/feed"
                      value={newWebhookUrl}
                      onChange={(e) => setNewWebhookUrl(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label>Events</Label>
                    <div className="space-y-2">
                      {[
                        "feed.updated",
                        "feed.error",
                        "feed.paused",
                        "feed.resumed",
                      ].map((event) => (
                        <div
                          key={event}
                          className="flex items-center space-x-2"
                        >
                          <input
                            type="checkbox"
                            id={`event-${event}`}
                            checked={newWebhookEvents.includes(event)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewWebhookEvents([
                                  ...newWebhookEvents,
                                  event,
                                ]);
                              } else {
                                setNewWebhookEvents(
                                  newWebhookEvents.filter((e) => e !== event)
                                );
                              }
                            }}
                          />
                          <Label htmlFor={`event-${event}`} className="text-sm">
                            {event}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateWebhookOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateWebhook}
                    disabled={!newWebhookUrl}
                  >
                    Add Webhook
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {webhookEndpoints.map((webhook) => (
              <div
                key={webhook.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div>
                  <div className="font-medium">{webhook.url}</div>
                  <div className="text-sm text-muted-foreground">
                    Events: {webhook.events.join(", ")}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Last delivery: {webhook.lastDelivery}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={
                      webhook.status === "active" ? "default" : "secondary"
                    }
                  >
                    {webhook.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    Test
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      {*/}
      <DeleteFeedSettings
        feedName={feedData.name}
        projectId={projectId}
        feedId={feedId}
      />
      {/* Feed Statistics */}
      {/*}
      
      {*/}
    </div>
  );
}
