"use client";
import React from "react";
import { useState } from "react";
import { Trash2, <PERSON>, Plus, <PERSON><PERSON>, <PERSON>O<PERSON>, <PERSON> } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { OneFeedData } from "@socialfeed/api/src/hono/manage/project/feeds/get";
import { createAPIKey } from "@/lib/actions/createAPIKey";
import { deleteAPIKey } from "@/lib/actions/deleteApiKey";

const ApiKeysFeedSettings = ({
  projectId,
  feedId,
  apiKeys,
}: {
  projectId: string;
  feedId: string;
  apiKeys: OneFeedData["apiKeys"];
}) => {
  const [showApiKeys, setShowApiKeys] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [isCreateKeyOpen, setIsCreateKeyOpen] = useState(false);

  const [newKeyName, setNewKeyName] = useState("");

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKeys((prev) => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const handleCopyApiKey = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    });
  };

  const handleCreateApiKey = async () => {
    await createAPIKey({
      projectId: projectId,
      feedId: feedId,
      name: newKeyName,
    });
    toast({
      title: "API key created",
      description: "New feed API key has been generated successfully.",
    });
    setIsCreateKeyOpen(false);
    setNewKeyName("");
  };

  const handleDeleteApiKey = async (key: string) => {
    await deleteAPIKey({
      projectId: projectId,
      feedId: feedId,
      key: key,
    });
    toast({
      title: "API key deleted",
      description: "The API key has been deleted successfully.",
    });
  };
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Key className="h-5 w-5" />
              <span>Feed API Keys</span>
            </CardTitle>
            <CardDescription>
              Manage API keys for accessing this feed's data.
            </CardDescription>
          </div>
          <Dialog open={isCreateKeyOpen} onOpenChange={setIsCreateKeyOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create API Key
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Feed API Key</DialogTitle>
                <DialogDescription>
                  Generate a new API key for accessing this feed.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="key-name">Key Name</Label>
                  <Input
                    id="key-name"
                    placeholder="e.g., Production Feed API Key"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  Feed API keys have read-only access to this specific feed's
                  data.
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateKeyOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleCreateApiKey} disabled={!newKeyName}>
                  Create Key
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Key</TableHead>
              <TableHead>Last Used</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {apiKeys.map((apiKey, i) => (
              <TableRow key={i}>
                <TableCell>
                  <div>
                    <div className="font-medium">{apiKey.description}</div>
                    <div className="text-sm text-muted-foreground">
                      Created {apiKey.createdAt}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <code className="text-sm font-mono">
                      {showApiKeys[apiKey.key]
                        ? apiKey.key
                        : "••••••••••••••••••••••••••••••••"}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleApiKeyVisibility(apiKey.key)}
                    >
                      {showApiKeys[apiKey.key] ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleCopyApiKey(apiKey.key)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>{apiKey.lastUsedAt ?? "never"}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      apiKey.status === "active" ? "default" : "secondary"
                    }
                  >
                    {apiKey.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete the API key "
                            {apiKey.description}". Applications using this key
                            will lose access immediately.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteApiKey(apiKey.key)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Delete Key
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default ApiKeysFeedSettings;
