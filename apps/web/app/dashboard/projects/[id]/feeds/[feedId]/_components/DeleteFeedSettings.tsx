"use client";
import React from "react";
import { Alert<PERSON><PERSON>gle, Trash2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { deleteFeed } from "@/lib/actions/deleteFeed";

const DeleteFeedSettings = ({
  feedName,
  projectId,
  feedId,
}: {
  feedName: string;
  projectId: string;
  feedId: string;
}) => {
  const handleDeleteFeed = async () => {
    await deleteFeed({
      projectId: projectId,
      feedId: feedId,
    });
    toast({
      title: "Feed Deleted",
      description: "The feed has been permanently deleted.",
      variant: "destructive",
    });
    window.location.href = `/dashboard/projects/${projectId}/feeds`;
  };

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-destructive">
          <AlertTriangle className="h-5 w-5" />
          <span>Danger Zone</span>
        </CardTitle>
        <CardDescription>
          Irreversible and destructive actions for this feed.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 border border-destructive rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-destructive">Delete Feed</h3>
              <p className="text-sm text-muted-foreground">
                Permanently delete this feed and all associated data. This
                action cannot be undone.
              </p>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Feed
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    the feed "{feedName}" and remove all associated data.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteFeed}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Yes, delete feed
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeleteFeedSettings;
