"use client";
import React from "react";
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { updateFeed } from "@/lib/actions/updateFeed";

const BasicFeedSettings = ({
  projectId,
  feedId,
  name,
  description,
  isActive: currentIsActive,
}: {
  projectId: string;
  feedId: string;
  name: string;
  description?: string;
  isActive: boolean;
}) => {
  const [feedName, setFeedName] = useState(name);
  const [feedDescription, setFeedDescription] = useState(description);
  const [isActive, setIsActive] = useState(currentIsActive);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const handleSaveSettings = async () => {
    await updateFeed({
      name: feedName,
      description: feedDescription,
      isActive,
      connections: null,
      projectId: projectId,
      feedId: feedId,
    });
    setHasUnsavedChanges(false);
    toast({
      title: "Feed updated",
      description: "Feed configuration has been saved successfully.",
    });
  };
  const handleInputChange =
    (setter: (value: string) => void) => (value: string) => {
      setter(value);
      setHasUnsavedChanges(true);
    };

  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked);
    setHasUnsavedChanges(true);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Settings</CardTitle>
        <CardDescription>
          Configure the basic properties of your feed.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="feed-name">Feed Name</Label>
          <Input
            id="feed-name"
            value={feedName}
            onChange={(e) => handleInputChange(setFeedName)(e.target.value)}
            placeholder="Enter feed name"
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="feed-description">Description</Label>
          <Textarea
            id="feed-description"
            value={feedDescription}
            onChange={(e) =>
              handleInputChange(setFeedDescription)(e.target.value)
            }
            placeholder="Enter feed description"
            rows={3}
          />
        </div>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="feed-status">Feed Status</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable this feed
            </p>
          </div>
          <Switch
            id="feed-status"
            checked={isActive}
            onCheckedChange={handleStatusToggle}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button disabled={!hasUnsavedChanges} onClick={handleSaveSettings}>
          Save
        </Button>
      </CardFooter>
    </Card>
  );
};

export default BasicFeedSettings;
