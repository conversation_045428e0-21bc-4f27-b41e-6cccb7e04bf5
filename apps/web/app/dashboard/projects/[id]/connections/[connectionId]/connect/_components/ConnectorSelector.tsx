"use client";
import { toast } from "@/hooks/use-toast";
import { getOneProjectConnectionReturnType } from "@socialfeed/api/src/hono/manage/project/connections/get";
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

const ConnectorSelector: React.FC<{
  connection: getOneProjectConnectionReturnType;
  token?: string;
}> = ({ connection, token }) => {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    if (!connection) return;

    setIsConnecting(true);

    try {
      // Redirect to OAuth flow with token
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8787";
      let oauthUrl: string;

      switch (connection.platform) {
        case "youtube":
          oauthUrl = `${apiUrl}/oauth/youtube/authorize?token=${token}`;
          break;
        case "instagram":
        case "instagram_business":
        case "instagram_with_facebook":
          window.location.href = `/dashboard/connect/instagram?connection_id=${connection.id}&token=${token}`;
          return;
        case "facebook":
          oauthUrl = `${apiUrl}/oauth/meta/authorize?token=${token}`;
          break;
        case "tiktok":
          oauthUrl = `${apiUrl}/oauth/tiktok/authorize?token=${token}`;
          break;
        default:
          throw new Error(`Platform ${connection.platform} not supported`);
      }

      // Redirect to OAuth flow
      window.location.href = oauthUrl;
    } catch (error) {
      console.error("Connection error:", error);
      toast({
        title: "Connection Error",
        description: "Failed to initiate connection. Please try again.",
        variant: "destructive",
      });
      setIsConnecting(false);
    }
  };

  const getPlatformDisplayName = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "YouTube";
      case "instagram":
        return "Instagram Personal";
      case "instagram_business":
        return "Instagram Business";
      case "instagram_with_facebook":
        return "Instagram with Facebook";
      case "facebook":
        return "Facebook";
      case "tiktok":
        return "TikTok";
      default:
        return platform;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "bg-red-500 hover:bg-red-600";
      case "instagram":
      case "instagram_business":
      case "instagram_with_facebook":
        return "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600";
      case "facebook":
        return "bg-blue-600 hover:bg-blue-700";
      case "tiktok":
        return "bg-black hover:bg-gray-900";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };
  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Connect Your Account</h1>
          <p className="text-muted-foreground">
            You've been invited to connect your{" "}
            {getPlatformDisplayName(connection.platform)} account
          </p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  {connection.name}
                  {connection.isConnected && (
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800"
                    >
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      Connected
                    </Badge>
                  )}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-10 h-10 rounded-lg ${getPlatformColor(connection.platform)} flex items-center justify-center text-white font-semibold`}
                  >
                    {getPlatformDisplayName(connection.platform).charAt(0)}
                  </div>
                  <div>
                    <h3 className="font-medium">
                      {getPlatformDisplayName(connection.platform)}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {connection.isConnected
                        ? `Connected as ${connection.platformAccountName}`
                        : "Not connected"}
                    </p>
                  </div>
                </div>

                <Button
                  onClick={handleConnect}
                  disabled={isConnecting}
                  className={`${getPlatformColor(connection.platform)} text-white`}
                >
                  {isConnecting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Connecting...
                    </>
                  ) : connection.isConnected ? (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Replace Account
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect Account
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-muted-foreground">
          <p>
            This is a secure connection managed by the project team.
            <br />
            Your account information will only be used for the intended purpose.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ConnectorSelector;
