"use client";

import { FacebookPageSelector } from "@/components/facebook/FacebookPageSelector";
import { useRouter } from "next/navigation";

interface FacebookPage {
  id: string;
  name: string;
  about?: string;
  fan_count?: number;
  followers_count?: number;
  picture?: {
    data: {
      url: string;
    };
  };
}

interface FacebookPageSelectionWrapperProps {
  projectId: string;
  connectionId: string;
  pages: FacebookPage[];
  currentPageId?: string | null;
}

export function FacebookPageSelectionWrapper({
  projectId,
  connectionId,
  pages,
  currentPageId,
}: FacebookPageSelectionWrapperProps) {
  const router = useRouter();

  const handlePageSelected = () => {
    // Redirect to connection details after successful page selection
    router.push(
      `/dashboard/projects/${projectId}/connections/${connectionId}?success=page-selected`
    );
  };

  return (
    <FacebookPageSelector
      projectId={projectId}
      connectionId={connectionId}
      pages={pages}
      currentPageId={currentPageId}
      onPageSelected={handlePageSelected}
    />
  );
}
