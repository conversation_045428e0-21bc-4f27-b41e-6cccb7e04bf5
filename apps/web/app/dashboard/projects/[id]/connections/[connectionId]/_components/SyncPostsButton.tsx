"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { syncPosts } from "@/lib/actions/syncPosts";
import { useParams } from "next/navigation";

const SyncPostsButton: React.FC<{
  disabled?: boolean;
  connectionId: string;
}> = ({ disabled, connectionId }) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const params = useParams();
  const projectId = params.id as string;

  const handleSyncPosts = async () => {
    setIsSyncing(true);

    try {
      const result = await syncPosts(projectId, connectionId);

      if (result.success) {
        toast({
          title: "Synchronization Started",
          description: "Your posts are being synchronized.",
        });
      } else {
        toast({
          title: "Synchronization Failed",
          description: result.error || "Failed to start synchronization.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Sync posts error:", error);
      toast({
        title: "Synchronization Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <Button
      variant="outline"
      onClick={handleSyncPosts}
      disabled={isSyncing || disabled}
    >
      {isSyncing ? (
        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4 mr-2" />
      )}
      Synchronize Posts
    </Button>
  );
};

export default SyncPostsButton;
