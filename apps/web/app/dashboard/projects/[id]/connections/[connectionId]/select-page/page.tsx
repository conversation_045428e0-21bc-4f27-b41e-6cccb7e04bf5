import { Suspense } from "react";
import { notFound, redirect } from "next/navigation";
import { getUser } from "@propelauth/nextjs/server/app-router";
import { FacebookPageSelectionWrapper } from "./_components/FacebookPageSelectionWrapper";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { getConnection } from "@/lib/fetch/getConnection";
import { getFacebookPages } from "@/lib/fetch/getFacebookPages";

interface PageProps {
  params: {
    id: string;
    connectionId: string;
  };
}

export default async function SelectFacebookPagePage({ params }: PageProps) {
  const user = await getUser();

  if (!user) {
    redirect("/auth/login");
  }

  const orgId = user.getActiveOrg()?.orgId;
  if (!orgId) {
    redirect("/dashboard");
  }

  const connection = await getConnection(params.id, params.connectionId);

  if (!connection) {
    notFound();
  }

  // Verify this is a Facebook connection that needs page selection
  if (connection.platform !== "facebook") {
    redirect(
      `/dashboard/projects/${params.id}/connections/${params.connectionId}`
    );
  }

  // Check if page selection is needed (handle case where field doesn't exist yet)
  const needsPageSelection =
    (connection as any).pendingPageSelection === true ||
    (connection.platform === "facebook" && !connection.platformAccountName);

  if (!needsPageSelection) {
    // Already has a page selected, redirect to connection details
    redirect(
      `/dashboard/projects/${params.id}/connections/${params.connectionId}`
    );
  }

  // Fetch Facebook pages
  const facebookPagesData = await getFacebookPages(
    params.id,
    params.connectionId
  );

  if (!facebookPagesData.success) {
    // Handle error case
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Error Loading Facebook Pages</CardTitle>
              <CardDescription>
                Failed to load your Facebook pages. Please try refreshing the
                page or reconnecting your Facebook account.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Page Header */}
          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <svg
                className="w-8 h-8 text-blue-600"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-gray-900">
              Complete Facebook Connection
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Your Facebook account has been successfully authenticated. Now
              select which Facebook Page you want to connect for content
              synchronization.
            </p>
          </div>

          {/* Business Portfolio Notice */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                  <svg
                    className="w-5 h-5 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  Business Portfolio Limitations
                </h3>
                <p className="text-blue-800 mb-4">
                  Some Facebook Pages may not appear in this list if they are
                  managed by a Meta Business Portfolio that we don't have
                  permissions to access.
                </p>
                <div className="bg-white/60 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">
                    If you don't see an expected page:
                  </h4>
                  <ul className="space-y-2 text-sm text-blue-800">
                    <li className="flex items-start space-x-2">
                      <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                      <span>
                        Ensure you have admin access to the Facebook Page
                      </span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                      <span>
                        Check if the page is managed through a Business
                        Portfolio
                      </span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                      <span>
                        If using Business Portfolio, ensure our app has the
                        necessary permissions
                      </span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-400 rounded-full mt-2"></span>
                      <span>
                        Try reconnecting your Facebook account if the page was
                        recently added
                      </span>
                    </li>
                  </ul>
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <p className="text-sm text-blue-800 mb-3">
                      If you're still missing pages, you can reconnect your
                      Facebook account to refresh permissions:
                    </p>
                    <a
                      href={`/dashboard/projects/${params.id}/connections/${params.connectionId}/connect`}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      Reconnect Facebook Account
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Page Selection Section */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-semibold text-gray-900">
                Select Your Facebook Page
              </CardTitle>
              <CardDescription className="text-base text-gray-600 mt-2">
                Choose the Facebook Page whose content you want to synchronize.
                You can change this selection later if needed.
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <Suspense
                fallback={
                  <div className="flex flex-col items-center justify-center py-12 space-y-4">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                    <span className="text-gray-600">
                      Loading your Facebook pages...
                    </span>
                  </div>
                }
              >
                <FacebookPageSelectionWrapper
                  projectId={params.id}
                  connectionId={params.connectionId}
                  pages={facebookPagesData.pages}
                  currentPageId={facebookPagesData.currentPageId}
                />
              </Suspense>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
