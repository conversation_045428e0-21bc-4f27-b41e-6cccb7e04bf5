"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw, TestTube } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { testConnection } from "@/lib/actions/testConnection";
import { useParams } from "next/navigation";

const TestConnectionButton: React.FC<{
  disabled?: boolean;
  platform: string;
  connectionId: string;
}> = ({ disabled, platform, connectionId }) => {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const params = useParams();
  const projectId = params.id as string;

  const handleTestConnection = async () => {
    setIsTestingConnection(true);

    try {
      const result = await testConnection(projectId, connectionId);

      console.log(result);

      if (result.success) {
        toast({
          title: "Connection Test Successful",
          description:
            result.message || `${platform} connection is working properly.`,
        });
      } else {
        toast({
          title: "Connection Test Failed",
          description: result.error || `${platform} connection test failed.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Connection test error:", error);
      toast({
        title: "Test Failed",
        description: `Failed to test ${platform} connection. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <Button
      variant="outline"
      onClick={handleTestConnection}
      disabled={isTestingConnection || disabled}
    >
      {isTestingConnection ? (
        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <TestTube className="h-4 w-4 mr-2" />
      )}
      Test Connection
    </Button>
  );
};

export default TestConnectionButton;
