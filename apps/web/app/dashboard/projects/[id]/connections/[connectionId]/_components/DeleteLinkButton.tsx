"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { deleteLink } from "@/lib/actions/deleteLink";

const DeleteLinkButton: React.FC<{
  disabled?: boolean;
  name: string;
  linkId: string;
  connectionId: string;
  projectId: string;
}> = ({ disabled, name, linkId, connectionId, projectId }) => {
  const handleDeleteLink = (linkId: string) => {
    deleteLink({ projectId, connectionId, linkId });
    toast({
      title: "Link Deleted",
      description: "OAuth link has been deleted.",
      variant: "destructive",
    });
  };
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          size="icon"
          variant="outline"
          title="Delete Link"
          disabled={disabled}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete OAuth Link?</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the link "{name}"? This action
            cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={() => handleDeleteLink(linkId)}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteLinkButton;
