"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Unlink, Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { disconnectConnection } from "@/lib/actions/disconnectConnection";
import { useParams, useRouter } from "next/navigation";

interface DisconnectButtonProps {
  disabled?: boolean;
  platform: string;
  connectionId: string;
  platformAccountName?: string;
}

export function DisconnectButton({
  disabled,
  platform,
  connectionId,
  platformAccountName,
}: DisconnectButtonProps) {
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;

  const handleDisconnect = async () => {
    setIsDisconnecting(true);
    
    try {
      const result = await disconnectConnection(projectId, connectionId);
      
      if (result.success) {
        toast({
          title: "Account Disconnected",
          description: result.message || `${platform} account has been disconnected successfully.`,
        });
        
        // Close the dialog
        setIsOpen(false);
        
        // Optionally refresh the page or redirect
        router.refresh();
      } else {
        toast({
          title: "Disconnect Failed",
          description: result.error || `Failed to disconnect ${platform} account.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Disconnect error:", error);
      toast({
        title: "Disconnect Failed",
        description: `Failed to disconnect ${platform} account. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsDisconnecting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          title="Disconnect Account"
          disabled={disabled}
        >
          <Unlink className="h-4 w-4" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Disconnect Account</AlertDialogTitle>
          <AlertDialogDescription>
            This will disconnect "{platformAccountName || "the account"}" from this connection. 
            Feeds using this connection will stop receiving data until you reconnect.
            <br /><br />
            <strong>Note:</strong> This will clear the OAuth tokens but keep the connection 
            configuration. You can reconnect the same or a different {platform} account later.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDisconnecting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDisconnect}
            disabled={isDisconnecting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDisconnecting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Disconnecting...
              </>
            ) : (
              "Disconnect"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
