"use client";
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { DateTime } from "luxon";
import { refreshToken } from "@/lib/actions/refreshToken";

export const DebugCard = ({
  projectId,
  connectionId,
  expiresAt,
}: {
  projectId: string;
  connectionId: string;
  expiresAt: Date | null;
}) => {
  console.log(expiresAt, "expiresAt in DebugCard", typeof expiresAt);

  const expiresAtDate =
    typeof expiresAt === "string" ? new Date(expiresAt) : expiresAt;

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [newExpiresAt, setNewExpiresAt] = useState<Date | null>(null);

  const handleRefreshToken = async () => {
    setIsRefreshing(true);
    try {
      const response = await refreshToken(
        projectId, // Replace with actual project ID
        connectionId
      );

      if (!response) {
        throw new Error("Failed to refresh token");
      }

      setNewExpiresAt(new Date(response.expiresAt));
      toast({
        title: "Token refreshed",
        description: "The token has been successfully refreshed.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh token.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const displayDate = newExpiresAt || expiresAtDate;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Debug Information</CardTitle>
        <CardDescription>
          Internal debugging information for this connection.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="font-medium">Token Expiration</h4>
          <p className="text-sm text-muted-foreground">
            {displayDate
              ? `Expires at ${DateTime.fromJSDate(displayDate).toFormat(
                  "yyyy-MM-dd HH:mm:ss"
                )}`
              : "No expiration date"}
          </p>
        </div>
        <Button onClick={handleRefreshToken} disabled={isRefreshing} size="sm">
          {isRefreshing ? "Refreshing..." : "Refresh Token"}
        </Button>
      </CardContent>
    </Card>
  );
};
