"use client";
import React from "react";
import { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { updateConnection } from "@/lib/actions/updateConnection";
import { getOneProjectConnectionReturnType } from "@socialfeed/api/src/hono/manage/project/connections/get";
import SyncPostsButton from "./SyncPostsButton";
import { RefreshCw } from "lucide-react";

export const BasicConnectionSettings = ({
  projectId,
  connection,
}: {
  projectId: string;
  connection: getOneProjectConnectionReturnType;
}) => {
  const {
    id: connectionId,
    name: currentName,
    isSyncing,
    isConnected,
    status,
    isActive: currentIsActive,
  } = connection;
  const [name, setName] = useState(currentName);
  const [isActive, setIsActive] = useState(currentIsActive);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const handleSaveSettings = async () => {
    await updateConnection({
      name: name,
      isActive,
      projectId: projectId,
      connectionId: connectionId,
    });
    setHasUnsavedChanges(false);
    toast({
      title: "Connection updated",
      description: "Connection configuration has been saved successfully.",
    });
  };
  const handleInputChange =
    (setter: (value: string) => void) => (value: string) => {
      setter(value);
      setHasUnsavedChanges(true);
    };

  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked);
    setHasUnsavedChanges(true);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Settings</CardTitle>
        <CardDescription>
          Configure the basic properties of your connection.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="name">Connection Name</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => handleInputChange(setName)(e.target.value)}
            placeholder="Enter connection name"
          />
        </div>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="status">Connection Status</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable this connection
            </p>
          </div>
          <Switch
            id="status"
            checked={isActive}
            onCheckedChange={handleStatusToggle}
          />
        </div>
        {isSyncing && (
          <div className="flex items-center text-sm text-muted-foreground">
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            <span>Synchronizing posts...</span>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button disabled={!hasUnsavedChanges} onClick={handleSaveSettings}>
          Save
        </Button>
        <SyncPostsButton
          connectionId={connectionId}
          disabled={!isConnected || isSyncing}
        />
      </CardFooter>
    </Card>
  );
};
