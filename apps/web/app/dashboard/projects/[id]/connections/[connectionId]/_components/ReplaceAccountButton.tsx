"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { replaceAccount } from "@/lib/actions/replaceAccount";
import { useParams } from "next/navigation";
import { RefreshCw } from "lucide-react";

const ReplaceAccountButton = ({ connectionId }: { connectionId: string }) => {
  const [isReplacing, setIsReplacing] = useState(false);
  const params = useParams();
  const projectId = params.id as string;

  const handleReplaceAccount = async () => {
    setIsReplacing(true);

    try {
      toast({
        title: "Initiating Account Replacement",
        description: "You'll be redirected to connect the new account.",
      });

      // Get the OAuth URL from the server action
      const result = await replaceAccount(projectId, connectionId);

      if (result.success && result.redirectUrl) {
        // Redirect to the OAuth flow
        window.location.href = result.redirectUrl;
      } else {
        throw new Error(result.error || "Failed to get OAuth URL");
      }
    } catch (error) {
      console.error("Replace account error:", error);
      toast({
        title: "Replacement Failed",
        description:
          "Failed to initiate account replacement. Please try again.",
        variant: "destructive",
      });
      setIsReplacing(false);
    }
  };

  return (
    <Button
      size="sm"
      variant="outline"
      onClick={handleReplaceAccount}
      disabled={isReplacing}
    >
      {isReplacing ? (
        <>
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Replacing...
        </>
      ) : (
        "Replace Account"
      )}
    </Button>
  );
};

export default ReplaceAccountButton;
