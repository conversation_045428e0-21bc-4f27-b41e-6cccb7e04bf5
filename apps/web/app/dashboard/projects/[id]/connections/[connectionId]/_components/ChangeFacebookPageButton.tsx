"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Settings, Loader2 } from "lucide-react";
import { FacebookPageSelector } from "@/components/facebook/FacebookPageSelector";
import { getFacebookPages } from "@/lib/fetch/getFacebookPages";
import { useRouter } from "next/navigation";

interface ChangeFacebookPageButtonProps {
  projectId: string;
  connectionId: string;
  disabled?: boolean;
}

interface FacebookPage {
  id: string;
  name: string;
  about?: string;
  fan_count?: number;
  followers_count?: number;
  picture?: {
    data: {
      url: string;
    };
  };
}

export function ChangeFacebookPageButton({
  projectId,
  connectionId,
  disabled,
}: ChangeFacebookPageButtonProps) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [pages, setPages] = useState<FacebookPage[]>([]);
  const [currentPageId, setCurrentPageId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handlePageSelected = () => {
    setOpen(false);
    // Refresh the page to show updated connection info
    router.refresh();
  };

  const fetchPages = async () => {
    if (loading) return;

    setLoading(true);
    try {
      const result = await getFacebookPages(projectId, connectionId);
      if (result.success) {
        setPages(result.pages);
        setCurrentPageId(result.currentPageId || null);
      }
    } catch (error) {
      console.error("Error fetching Facebook pages:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch pages when dialog opens
  useEffect(() => {
    if (open) {
      fetchPages();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" disabled={disabled}>
          <Settings className="h-4 w-4 mr-2" />
          Change Page
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Change Facebook Page</DialogTitle>
          <DialogDescription>
            Select a different Facebook Page to connect for content
            synchronization. This will replace the currently connected page.
          </DialogDescription>
        </DialogHeader>
        <div className="mt-4">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="text-gray-600">
                Loading your Facebook pages...
              </span>
            </div>
          ) : (
            <FacebookPageSelector
              projectId={projectId}
              connectionId={connectionId}
              pages={pages}
              currentPageId={currentPageId}
              onPageSelected={handlePageSelected}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
