import { getConnection } from "@/lib/fetch/getConnection";
import { notFound } from "next/navigation";
import React from "react";
import { InstagramConnector } from "@/components/oauth/instagram-connector";
import { FacebookConnector } from "@/components/oauth/facebook-connector";
import { YouTubeConnector } from "@/components/oauth/youtube-connector";
import { TikTokConnector } from "@/components/oauth/tiktok-connector";

const ConnectConnectionPage = async ({
  params,
  searchParams,
}: {
  params: Promise<{ id: string; connectionId: string }>;
  searchParams: Promise<{ token?: string }>;
}) => {
  const { id, connectionId } = await params;
  const { token } = await searchParams;

  try {
    const connection = await getConnection(id, connectionId);

    // Check if connection is already connected
    if (connection.isConnected && connection.status === "active") {
      return (
        <div className="max-w-2xl mx-auto text-center space-y-4">
          <h1 className="text-2xl font-bold">Already Connected</h1>
          <p className="text-muted-foreground">
            This {connection.platform} connection is already active.
          </p>
          <p className="text-sm text-muted-foreground">
            Account: {connection.platformAccountName || "Unknown"}
          </p>
        </div>
      );
    }

    switch (connection.platform) {
      case "instagram":
      case "instagram_business":
      case "instagram_with_facebook":
        return <InstagramConnector connectionId={connectionId} token={token} />;
      case "facebook":
        return <FacebookConnector connectionId={connectionId} token={token} />;
      case "youtube":
        return <YouTubeConnector connectionId={connectionId} token={token} />;
      case "tiktok":
        return <TikTokConnector connectionId={connectionId} token={token} />;
      default:
        return (
          <div className="max-w-2xl mx-auto text-center space-y-4">
            <h1 className="text-2xl font-bold">Platform Not Supported</h1>
            <p className="text-muted-foreground">
              The platform "{connection.platform}" is not yet supported for
              OAuth connections.
            </p>
          </div>
        );
    }
  } catch (error) {
    console.error("Error fetching connection:", error);
    notFound();
  }
};

export default ConnectConnectionPage;
