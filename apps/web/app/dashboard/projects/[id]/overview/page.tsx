import ProjectConnectedNetworks from "@/components/dashboard/cards/ProjectConnectedNetworks";
import ProjectCostCard from "@/components/dashboard/cards/ProjectCostCard";
import ProjectFeedsCard from "@/components/dashboard/cards/ProjectFeeds";
import ProjectRequestsCard from "@/components/dashboard/cards/ProjectRequestsCard";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getProject } from "@/lib/fetch/getProject";

// Mock data for demonstration
const MOCK_PROJECT_OVERVIEW_DATA = {
  totalConnections: 5,
  activeFeeds: 3,
  recentActivity: [
    "Feed 'Product Updates' synced successfully.",
    "New connection 'Twitter - Company X' added.",
    "API Key for 'Main Feed' accessed.",
  ],
};

export default async function ProjectOverviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const projectId = (await params).id;

  // Fetch actual overview data here based on projectId
  const overviewData = MOCK_PROJECT_OVERVIEW_DATA;

  const data = await getProject({ projectId });

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <ProjectFeedsCard projectId={projectId} />
        <ProjectConnectedNetworks projectId={projectId} />
        <ProjectRequestsCard />
        <ProjectCostCard />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Latest events and actions within this project.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {overviewData.recentActivity.length > 0 ? (
            <ul className="space-y-2 text-sm text-muted-foreground">
              {overviewData.recentActivity.map((activity, index) => (
                <li key={index} className="border-l-2 pl-3 border-primary">
                  {activity}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-muted-foreground">No recent activity.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
