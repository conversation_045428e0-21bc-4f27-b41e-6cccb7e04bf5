"use client";

import { Trash2, <PERSON><PERSON><PERSON>rian<PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";
import { deleteProject } from "@/lib/actions/deleteProject";

export const DeleteProjectSettings = ({
  projectName,
  projectId,
}: {
  projectName: string;
  projectId: string;
}) => {
  const handleDeleteProject = () => {
    deleteProject({ projectId });
    toast({
      title: "Project Deleted",
      description: "The project has been permanently deleted.",
      variant: "destructive",
    });
    window.location.href = "/dashboard/projects";
    // In a real app, navigate away or update UI
  };
  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-destructive">
          <AlertTriangle className="h-5 w-5" />
          <span>Danger Zone</span>
        </CardTitle>
        <CardDescription>
          Irreversible and destructive actions for this project.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 border border-destructive rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-destructive">Delete Project</h3>
              <p className="text-sm text-muted-foreground">
                Permanently delete this project and all associated data. This
                action cannot be undone.
              </p>
            </div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Project
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    the project "{projectName}" and remove all associated data.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteProject}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Yes, delete project
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
