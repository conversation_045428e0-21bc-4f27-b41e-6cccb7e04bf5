"use client";
import React from "react";
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

export const BasicProjectSettings = ({
  name,
  description,
  isActive: currentIsActive,
  projectId,
}: {
  name: string;
  description: string | null;
  isActive: boolean;
  projectId: string;
}) => {
  const [projectName, setProjectName] = useState(name);
  const [projectDescription, setProjectDescription] = useState(description);
  const [isActive, setIsActive] = useState(currentIsActive);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleProjectNameChange = (value: string) => {
    setProjectName(value);
    setHasUnsavedChanges(true);
  };

  const handleProjectDescriptionChange = (value: string) => {
    setProjectDescription(value);
    setHasUnsavedChanges(true);
  };
  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked);
    setHasUnsavedChanges(true);
  };

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false);
    toast({
      title: "Settings Saved",
      description: "Project settings have been updated successfully.",
    });
    // In a real app, you would persist these changes
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Information</CardTitle>
        <CardDescription>
          Update your project name and description.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="project-name">Project Name</Label>
          <Input
            id="project-name"
            value={projectName}
            onChange={(e) => handleProjectNameChange(e.target.value)}
            placeholder="Enter project name"
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="project-description">Description</Label>
          <Textarea
            id="project-description"
            value={projectDescription ?? ""}
            onChange={(e) => handleProjectDescriptionChange(e.target.value)}
            placeholder="Enter project description"
            rows={3}
          />
        </div>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="feed-status">Feed Status</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable this feed
            </p>
          </div>
          <Switch
            id="feed-status"
            checked={isActive}
            onCheckedChange={handleStatusToggle}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button disabled={!hasUnsavedChanges} onClick={handleSaveSettings}>
          Save
        </Button>
      </CardFooter>
    </Card>
  );
};
