import { getProject } from "@/lib/fetch/getProject";
import { BasicProjectSettings } from "./_components/BasicProjectSettings";
import { DeleteProjectSettings } from "./_components/DeleteProjectSettings";

export default async function ProjectSettingsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const project = await getProject({ projectId: id });
  if (!project) return null;
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <BasicProjectSettings
        name={project.name}
        description={project.description}
        isActive={project.isActive}
        projectId={project.id}
      />
      <DeleteProjectSettings
        projectName={project.name}
        projectId={project.id}
      />
    </div>
  );
}
