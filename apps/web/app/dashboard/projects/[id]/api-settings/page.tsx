"use client"

import { useState } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { ArrowLeft, Copy, Eye, EyeOff, Key, Plus, RefreshCw, Save, Trash2, Webhook } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/hooks/use-toast"
import Link from "next/link"

// Mock data
const projectData = {
  name: "PLAZE Feed",
  id: "proj_abc123",
}

const apiKeys = [
  {
    id: "key_001",
    name: "Production API Key",
    key: "pk_live_1234567890abcdef",
    created: "2023-10-15",
    lastUsed: "2 hours ago",
    status: "active",
    permissions: ["read", "write"],
  },
  {
    id: "key_002",
    name: "Development API Key",
    key: "pk_test_abcdef1234567890",
    created: "2023-11-01",
    lastUsed: "1 day ago",
    status: "active",
    permissions: ["read"],
  },
  {
    id: "key_003",
    name: "Legacy API Key",
    key: "pk_live_oldkey123456789",
    created: "2023-08-20",
    lastUsed: "30 days ago",
    status: "inactive",
    permissions: ["read", "write"],
  },
]

const webhookEndpoints = [
  {
    id: "webhook_001",
    url: "https://api.example.com/webhooks/social-updates",
    events: ["connection.created", "connection.failed", "data.updated"],
    status: "active",
    lastDelivery: "5 minutes ago",
  },
  {
    id: "webhook_002",
    url: "https://staging.example.com/webhooks/test",
    events: ["connection.created"],
    status: "active",
    lastDelivery: "2 hours ago",
  },
]

export default function APISettingsPage() {
  const params = useParams()
  const projectId = params.id as string

  const [showApiKeys, setShowApiKeys] = useState<{ [key: string]: boolean }>({})
  const [isCreateKeyOpen, setIsCreateKeyOpen] = useState(false)
  const [isCreateWebhookOpen, setIsCreateWebhookOpen] = useState(false)
  const [newKeyName, setNewKeyName] = useState("")
  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>([])
  const [newWebhookUrl, setNewWebhookUrl] = useState("")
  const [newWebhookEvents, setNewWebhookEvents] = useState<string[]>([])
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const toggleApiKeyVisibility = (keyId: string) => {
    setShowApiKeys((prev) => ({ ...prev, [keyId]: !prev[keyId] }))
  }

  const handleCopyApiKey = (key: string) => {
    navigator.clipboard.writeText(key)
    toast({
      title: "API key copied",
      description: "The API key has been copied to your clipboard.",
    })
  }

  const handleCreateApiKey = () => {
    // Mock creation logic
    toast({
      title: "API key created",
      description: "New API key has been generated successfully.",
    })
    setIsCreateKeyOpen(false)
    setNewKeyName("")
    setNewKeyPermissions([])
  }

  const handleCreateWebhook = () => {
    // Mock creation logic
    toast({
      title: "Webhook created",
      description: "New webhook endpoint has been configured successfully.",
    })
    setIsCreateWebhookOpen(false)
    setNewWebhookUrl("")
    setNewWebhookEvents([])
  }

  const handleSaveSettings = () => {
    setHasUnsavedChanges(false)
    toast({
      title: "Settings saved",
      description: "API settings have been updated successfully.",
    })
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <Link href={`/project/${projectId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Project
              </Button>
            </Link>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-3">
              <Key className="h-5 w-5" />
              <h1 className="text-xl font-semibold">API Settings</h1>
              <Badge variant="outline">{projectData.id}</Badge>
            </div>
          </div>
          <div className="ml-auto">
            <Button onClick={handleSaveSettings} disabled={!hasUnsavedChanges} className="mr-2">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
            {hasUnsavedChanges && <Badge variant="secondary">Unsaved changes</Badge>}
          </div>
        </div>
      </div>

      <div className="p-6 max-w-6xl mx-auto space-y-6">
        {/* API Keys Management */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Key className="h-5 w-5" />
                  <span>API Keys</span>
                </CardTitle>
                <CardDescription>Manage API keys for accessing your project's data.</CardDescription>
              </div>
              <Dialog open={isCreateKeyOpen} onOpenChange={setIsCreateKeyOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create API Key
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New API Key</DialogTitle>
                    <DialogDescription>Generate a new API key for your application.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="key-name">Key Name</Label>
                      <Input
                        id="key-name"
                        placeholder="e.g., Production API Key"
                        value={newKeyName}
                        onChange={(e) => setNewKeyName(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label>Permissions</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="read-permission"
                            checked={newKeyPermissions.includes("read")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewKeyPermissions([...newKeyPermissions, "read"])
                              } else {
                                setNewKeyPermissions(newKeyPermissions.filter((p) => p !== "read"))
                              }
                            }}
                          />
                          <Label htmlFor="read-permission">Read access</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="write-permission"
                            checked={newKeyPermissions.includes("write")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewKeyPermissions([...newKeyPermissions, "write"])
                              } else {
                                setNewKeyPermissions(newKeyPermissions.filter((p) => p !== "write"))
                              }
                            }}
                          />
                          <Label htmlFor="write-permission">Write access</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateKeyOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateApiKey} disabled={!newKeyName}>
                      Create Key
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Key</TableHead>
                  <TableHead>Permissions</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {apiKeys.map((apiKey) => (
                  <TableRow key={apiKey.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{apiKey.name}</div>
                        <div className="text-sm text-muted-foreground">Created {apiKey.created}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <code className="text-sm font-mono">
                          {showApiKeys[apiKey.id] ? apiKey.key : "••••••••••••••••"}
                        </code>
                        <Button size="sm" variant="ghost" onClick={() => toggleApiKeyVisibility(apiKey.id)}>
                          {showApiKeys[apiKey.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => handleCopyApiKey(apiKey.key)}>
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        {apiKey.permissions.map((permission) => (
                          <Badge key={permission} variant="outline" className="text-xs">
                            {permission}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>{apiKey.lastUsed}</TableCell>
                    <TableCell>
                      <Badge variant={apiKey.status === "active" ? "default" : "secondary"}>{apiKey.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button size="sm" variant="outline">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete the API key "{apiKey.name}". Applications using this key
                                will lose access immediately.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                                Delete Key
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Webhooks */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Webhook className="h-5 w-5" />
                  <span>Webhooks</span>
                </CardTitle>
                <CardDescription>Configure webhook endpoints to receive real-time notifications.</CardDescription>
              </div>
              <Dialog open={isCreateWebhookOpen} onOpenChange={setIsCreateWebhookOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Webhook
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Webhook Endpoint</DialogTitle>
                    <DialogDescription>Configure a new webhook endpoint to receive notifications.</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="webhook-url">Endpoint URL</Label>
                      <Input
                        id="webhook-url"
                        placeholder="https://your-app.com/webhooks"
                        value={newWebhookUrl}
                        onChange={(e) => setNewWebhookUrl(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label>Events</Label>
                      <div className="space-y-2">
                        {["connection.created", "connection.failed", "data.updated", "link.expired"].map((event) => (
                          <div key={event} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`event-${event}`}
                              checked={newWebhookEvents.includes(event)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setNewWebhookEvents([...newWebhookEvents, event])
                                } else {
                                  setNewWebhookEvents(newWebhookEvents.filter((e) => e !== event))
                                }
                              }}
                            />
                            <Label htmlFor={`event-${event}`} className="text-sm">
                              {event}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCreateWebhookOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateWebhook} disabled={!newWebhookUrl}>
                      Add Webhook
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {webhookEndpoints.map((webhook) => (
                <div key={webhook.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium">{webhook.url}</div>
                    <div className="text-sm text-muted-foreground">Events: {webhook.events.join(", ")}</div>
                    <div className="text-sm text-muted-foreground">Last delivery: {webhook.lastDelivery}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={webhook.status === "active" ? "default" : "secondary"}>{webhook.status}</Badge>
                    <Button size="sm" variant="outline">
                      Test
                    </Button>
                    <Button size="sm" variant="outline">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
