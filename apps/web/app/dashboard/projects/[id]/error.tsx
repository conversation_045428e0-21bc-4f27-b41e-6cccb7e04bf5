"use client";
import { AlertTriangle } from "lucide-react";

export default function ErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-red-50 to-orange-50">
      <div className="w-full max-w-md space-y-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Page Not Found</h3>
          <p className="text-muted-foreground mt-2">
            The page you are looking for does not exist.
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          Please check the URL or return to the dashboard.
        </div>
      </div>
    </div>
  );
}
