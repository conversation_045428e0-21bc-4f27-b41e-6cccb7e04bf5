import { Calendar, Clock, ExternalLink, LayoutDashboard } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import StatusIcon from "@/components/primitives/StatusIcon";
import StatusBadge from "@/components/primitives/StatusBadge";
import DashboardHeader from "@/components/primitives/DashboardHeader";
import TotalProjectsCard from "@/components/dashboard/cards/TotalProjectsCard";
import TotalRequestsCard from "@/components/dashboard/cards/TotalRequestsCard";
import TotalCostCard from "@/components/dashboard/cards/TotalCostCard";
import TotalConnectedNetworks from "@/components/dashboard/cards/TotalConnectedNetworks";
import ProtectedRoute from "@/lib/auth/ProtectedRoute";
import CreateProjectButton from "./_components/CreateProjectButton";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getProjects } from "@/lib/fetch/getProjects";
import { Badge } from "@/components/ui/badge";

export default async function ProjectsOverview() {
  const proj = await getProjects();

  return (
    <ProtectedRoute>
      <div className="flex flex-1 flex-col gap-4 p-6">
        <DashboardHeader
          title="Projects"
          icon={<LayoutDashboard className="size-9" />}
        />
        <div className="flex-1 space-y-6">
          {/* Account Overview Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <TotalProjectsCard />
            <TotalConnectedNetworks />
            <TotalRequestsCard />
            <TotalCostCard />
          </div>
          {/* Projects List */}
          <Card className="space-y-4">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Your Projects</CardTitle>
                  <CardDescription>
                    Manage your social media analytics projects and monitor
                    their performance.
                  </CardDescription>
                </div>
                <CreateProjectButton />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {proj.length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    You have no projects yet.
                  </p>
                )}
                {proj.map((project) => {
                  /*
                  const networks = JSON.parse(project.platforms);
                  const activatedNetworks = networks.filter(
                    (n: any) => n.status === "active"
                  );
                  */
                  const activeNetworks = project.platformConnections.filter(
                    (n: any) => n.status === "active"
                  );
                  const inactiveNetworks = project.platformConnections.filter(
                    (n: any) => n.status === "inactive"
                  );
                  const authNeededNetworks = project.platformConnections.filter(
                    (n: any) => n.status === "auth_needed"
                  );
                  const expiredNetworks = project.platformConnections.filter(
                    (n: any) => n.status === "expired"
                  );
                  const errorNetworks = project.platformConnections.filter(
                    (n: any) => n.status === "error"
                  );

                  const lastActivity = project.platformConnections.reduce(
                    (acc, conn) => {
                      const lastPolledAt = conn.lastPolledAt
                        ? new Date(conn.lastPolledAt)
                        : null;
                      return lastPolledAt && lastPolledAt > acc
                        ? lastPolledAt
                        : acc;
                    },
                    new Date(0)
                  );
                  const lastActivityString =
                    lastActivity > new Date(0)
                      ? lastActivity.toLocaleDateString()
                      : "never";

                  return (
                    <Card key={project.id} className="border border-border">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-3 flex-1">
                            <div className="flex items-center space-x-3">
                              <h3 className="text-lg font-semibold">
                                {project.name}
                              </h3>
                              <StatusIcon
                                status={
                                  project.isActive ? "active" : "inactive"
                                }
                              />
                              <StatusBadge
                                status={
                                  project.isActive ? "active" : "inactive"
                                }
                              />
                            </div>
                            <p className="text-muted-foreground">
                              {project.description}
                            </p>
                            {/* Network Status Indicators */}
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-muted-foreground">
                                  Connections:
                                </span>
                                <span className="text-sm text-muted-foreground flex gap-1">
                                  <Badge
                                    variant="secondary"
                                    className="bg-green-200"
                                  >
                                    {activeNetworks.length} Active
                                  </Badge>
                                  <Badge
                                    variant="secondary"
                                    className="bg-red-200"
                                  >
                                    {inactiveNetworks.length} Inactive
                                  </Badge>
                                  <Badge
                                    variant="secondary"
                                    className="bg-yellow-200"
                                  >
                                    {authNeededNetworks.length +
                                      expiredNetworks.length}{" "}
                                    Auth needed
                                  </Badge>
                                  <Badge
                                    variant="secondary"
                                    className="bg-red-300"
                                  >
                                    {errorNetworks.length} Error
                                  </Badge>
                                </span>
                              </div>
                              {/* <NetworkIndicators project={project} /> */}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-4 w-4" />
                                {project.createdAt && (
                                  <span>
                                    Created{" "}
                                    {new Date(
                                      project.createdAt
                                    ).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-4 w-4" />
                                <span>Last activity {lastActivityString}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Link
                              href={`/dashboard/projects/${project.id}/overview`}
                            >
                              <Button>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Open Project
                              </Button>
                            </Link>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
