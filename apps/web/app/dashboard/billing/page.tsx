import {
  Calendar,
  CreditCard,
  DollarSign,
  Download,
  FileText,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import {
  ChangePlanButton,
  DownloadInvoiceButton,
} from "./_components/BillingActions";

// Mock data
const billingInfo = {
  currentPlan: "Professional",
  billingCycle: "Monthly",
  nextBillingDate: "2024-01-15",
  currentUsage: {
    requests: 45672,
    requestsLimit: 100000,
    projects: 3,
    projectsLimit: 10,
  },
  paymentMethod: {
    type: "Visa",
    last4: "4242",
    expiryMonth: "12",
    expiryYear: "2025",
  },
};

const invoices = [
  {
    id: "inv_001",
    date: "2023-12-15",
    amount: 99.0,
    status: "paid",
    description: "Professional Plan - December 2023",
    downloadUrl: "#",
  },
  {
    id: "inv_002",
    date: "2023-11-15",
    amount: 99.0,
    status: "paid",
    description: "Professional Plan - November 2023",
    downloadUrl: "#",
  },
  {
    id: "inv_003",
    date: "2023-10-15",
    amount: 99.0,
    status: "paid",
    description: "Professional Plan - October 2023",
    downloadUrl: "#",
  },
  {
    id: "inv_004",
    date: "2023-09-15",
    amount: 49.0,
    status: "paid",
    description: "Starter Plan - September 2023",
    downloadUrl: "#",
  },
];

const plans = [
  {
    name: "Starter",
    price: 49,
    features: ["Up to 25,000 requests/month", "3 projects", "Basic support"],
    current: false,
  },
  {
    name: "Professional",
    price: 99,
    features: [
      "Up to 100,000 requests/month",
      "10 projects",
      "Priority support",
      "Advanced analytics",
    ],
    current: true,
  },
  {
    name: "Enterprise",
    price: 299,
    features: [
      "Unlimited requests",
      "Unlimited projects",
      "24/7 support",
      "Custom integrations",
    ],
    current: false,
  },
];

export default function BillingPage() {
  const getStatusBadge = (status: string) => {
    const variants = {
      paid: "default",
      pending: "secondary",
      failed: "destructive",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  const usagePercentage =
    (billingInfo.currentUsage.requests /
      billingInfo.currentUsage.requestsLimit) *
    100;

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="flex-1 space-y-6">
        {/* Current Plan & Usage */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>Current Plan</span>
              </CardTitle>
              <CardDescription>
                Your current subscription details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Plan</span>
                <Badge>{billingInfo.currentPlan}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Billing Cycle</span>
                <span>{billingInfo.billingCycle}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Next Billing Date</span>
                <span>
                  {new Date(billingInfo.nextBillingDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Amount</span>
                <span className="text-lg font-bold">$99.00</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Usage This Month</CardTitle>
              <CardDescription>Your current usage and limits</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">API Requests</span>
                  <span className="text-sm text-muted-foreground">
                    {billingInfo.currentUsage.requests.toLocaleString()} /{" "}
                    {billingInfo.currentUsage.requestsLimit.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full"
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {Math.round(usagePercentage)}% used
                </p>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Projects</span>
                <span>
                  {billingInfo.currentUsage.projects} /{" "}
                  {billingInfo.currentUsage.projectsLimit}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Method */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>Payment Method</span>
            </CardTitle>
            <CardDescription>Your default payment method</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">VISA</span>
                </div>
                <div>
                  <div className="font-medium">
                    •••• •••• •••• {billingInfo.paymentMethod.last4}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Expires {billingInfo.paymentMethod.expiryMonth}/
                    {billingInfo.paymentMethod.expiryYear}
                  </div>
                </div>
              </div>
              <Button variant="outline">Update</Button>
            </div>
          </CardContent>
        </Card>

        {/* Available Plans */}
        <Card>
          <CardHeader>
            <CardTitle>Available Plans</CardTitle>
            <CardDescription>
              Choose the plan that best fits your needs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {plans.map((plan) => (
                <Card
                  key={plan.name}
                  className={plan.current ? "border-primary" : ""}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{plan.name}</CardTitle>
                      {plan.current && <Badge>Current</Badge>}
                    </div>
                    <div className="text-3xl font-bold">
                      ${plan.price}
                      <span className="text-sm font-normal">/month</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <ul className="space-y-2">
                      {plan.features.map((feature, index) => (
                        <li
                          key={index}
                          className="text-sm flex items-center space-x-2"
                        >
                          <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <ChangePlanButton
                      planName={plan.name}
                      current={plan.current}
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Invoice History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Invoice History</span>
            </CardTitle>
            <CardDescription>
              Download your past invoices and receipts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {new Date(invoice.date).toLocaleDateString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{invoice.description}</TableCell>
                    <TableCell className="font-medium">
                      ${invoice.amount.toFixed(2)}
                    </TableCell>
                    <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                    <TableCell>
                      <DownloadInvoiceButton invoiceId={invoice.id} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
