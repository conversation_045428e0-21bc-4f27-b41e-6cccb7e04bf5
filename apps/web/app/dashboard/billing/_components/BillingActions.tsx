"use client";

import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Download } from "lucide-react";

export const DownloadInvoiceButton = ({ invoiceId }: { invoiceId: string }) => {
  const handleDownloadInvoice = (invoiceId: string) => {
    toast({
      title: "Download started",
      description: "Your invoice is being downloaded.",
    });
  };

  return (
    <Button
      size="sm"
      variant="outline"
      onClick={() => handleDownloadInvoice(invoiceId)}
    >
      <Download className="h-4 w-4 mr-2" />
      Download
    </Button>
  );
};

export const ChangePlanButton = ({
  planName,
  current,
}: {
  planName: string;
  current: boolean;
}) => {
  const handleChangePlan = (planName: string) => {
    toast({
      title: "Plan change requested",
      description: `Your plan will be changed to ${planName} on your next billing cycle.`,
    });
  };

  return (
    <Button
      className="w-full"
      variant={current ? "outline" : "default"}
      disabled={current}
      onClick={() => handleChangePlan(planName)}
    >
      {current ? "Current Plan" : "Upgrade"}
    </Button>
  );
};
