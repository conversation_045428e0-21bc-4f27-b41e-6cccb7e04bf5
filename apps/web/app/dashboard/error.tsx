"use client"; // Wichtig!

import { AlertTriangle } from "lucide-react";
import Link from "next/link";
import { useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Optional: <PERSON><PERSON><PERSON> an einen Error-Reporting-Dienst
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-red-50 to-orange-50">
      <div className="w-full max-w-md space-y-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Page Not Found</h3>
          <p className="text-muted-foreground mt-2">
            The page you are looking for does not exist.
          </p>
        </div>
        <div className="text-sm pb-4 text-muted-foreground">
          Please check the URL or return to the dashboard.
        </div>
        <Link
          href="/dashboard/projects"
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80"
        >
          Zurück zum Dashboard
        </Link>
      </div>
    </div>
  );
}
