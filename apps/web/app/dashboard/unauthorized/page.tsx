import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Shield, ArrowLeft, Mail, HelpCircle } from "lucide-react";
import Link from "next/link";

export default function UnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-red-50 to-orange-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <Shield className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Access Denied
          </CardTitle>
          <CardDescription className="text-gray-600">
            You don't have permission to access this resource
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center text-sm text-gray-500">
            <p>
              This page or feature requires additional permissions that your
              current role doesn't include.
            </p>
          </div>

          <div className="space-y-3">
            <Button asChild className="w-full" variant="default">
              <Link href="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Dashboard
              </Link>
            </Button>

            <Button asChild className="w-full" variant="outline">
              <Link href="/help">
                <HelpCircle className="mr-2 h-4 w-4" />
                Get Help
              </Link>
            </Button>

            <Button asChild className="w-full" variant="ghost">
              <Link href="/team">
                <Mail className="mr-2 h-4 w-4" />
                Contact Team Admin
              </Link>
            </Button>
          </div>

          <div className="rounded-lg bg-blue-50 p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Need access?
            </h4>
            <p className="text-xs text-blue-700">
              Contact your team administrator to request the necessary
              permissions for this feature.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
