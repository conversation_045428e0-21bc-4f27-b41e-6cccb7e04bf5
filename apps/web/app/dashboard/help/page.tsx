"use client"

import { useState } from "react"
import {
  Search,
  Book,
  MessageCircle,
  Mail,
  Phone,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  HelpCircle,
  Zap,
  Users,
  DollarSign,
  Building,
  LinkIcon,
  BarChart3,
  Shield,
  Clock,
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"

// FAQ data
const faqData = [
  {
    category: "Getting Started",
    icon: Zap,
    questions: [
      {
        question: "How do I create my first project?",
        answer:
          "Navigate to the Projects page and click 'Create Project'. Choose your project name, select the social media networks you want to connect, and follow the setup wizard.",
      },
      {
        question: "What social media platforms are supported?",
        answer:
          "We currently support Facebook, Instagram, Twitter, LinkedIn, YouTube, TikTok, and Pinterest. More platforms are being added regularly.",
      },
      {
        question: "How do I connect my social media accounts?",
        answer:
          "In your project dashboard, click on any network card and follow the OAuth authentication flow to securely connect your accounts.",
      },
    ],
  },
  {
    category: "Projects & Networks",
    icon: Building,
    questions: [
      {
        question: "Can I have multiple projects?",
        answer:
          "Yes! You can create unlimited projects on paid plans. Each project can have different network configurations and team access.",
      },
      {
        question: "How do I manage network permissions?",
        answer:
          "Go to your project settings and click on 'Network Settings'. You can modify permissions, refresh tokens, or disconnect networks from there.",
      },
      {
        question: "What happens if a network connection fails?",
        answer:
          "You'll receive notifications about connection issues. Most problems can be resolved by re-authenticating through the network settings page.",
      },
    ],
  },
  {
    category: "Team Management",
    icon: Users,
    questions: [
      {
        question: "How do I invite team members?",
        answer:
          "Go to the Team page and click 'Invite Member'. Enter their email address and select their role (Admin, Editor, or Viewer).",
      },
      {
        question: "What are the different team roles?",
        answer:
          "Admin: Full access to all features. Editor: Can manage projects and content but not billing. Viewer: Read-only access to projects and analytics.",
      },
      {
        question: "Can I limit team member access to specific projects?",
        answer:
          "Yes, you can assign team members to specific projects and control their access level per project in the project settings.",
      },
    ],
  },
  {
    category: "Billing & Usage",
    icon: DollarSign,
    questions: [
      {
        question: "How is usage calculated?",
        answer:
          "Usage is based on API calls, data sync operations, and active network connections. You can view detailed usage metrics in the Billing section.",
      },
      {
        question: "What happens if I exceed my plan limits?",
        answer:
          "You'll receive notifications as you approach limits. Exceeding limits may temporarily pause some features until you upgrade or usage resets.",
      },
      {
        question: "Can I change my plan anytime?",
        answer:
          "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and billing is prorated.",
      },
    ],
  },
  {
    category: "API & Integration",
    icon: LinkIcon,
    questions: [
      {
        question: "How do I generate API keys?",
        answer:
          "Go to API Settings in your project dashboard. Click 'Generate New Key', set permissions and expiration, then copy your key securely.",
      },
      {
        question: "What are webhook endpoints used for?",
        answer:
          "Webhooks allow real-time notifications when data changes occur. Configure them in API Settings to receive updates about posts, metrics, or connection status.",
      },
      {
        question: "Is there rate limiting on the API?",
        answer: "Yes, rate limits vary by plan. You can view your current limits and usage in the API Settings page.",
      },
    ],
  },
]

// Contact options
const contactOptions = [
  {
    title: "Live Chat",
    description: "Get instant help from our support team",
    icon: MessageCircle,
    action: "Start Chat",
    available: "24/7",
    badge: "Fastest",
  },
  {
    title: "Email Support",
    description: "Send us a detailed message",
    icon: Mail,
    action: "Send Email",
    available: "Response within 4 hours",
    badge: null,
  },
  {
    title: "Phone Support",
    description: "Speak directly with our team",
    icon: Phone,
    action: "Schedule Call",
    available: "Business hours only",
    badge: "Premium",
  },
]

// Resource links
const resources = [
  {
    title: "API Documentation",
    description: "Complete API reference and examples",
    icon: Book,
    url: "#",
    external: true,
  },
  {
    title: "Video Tutorials",
    description: "Step-by-step video guides",
    icon: BarChart3,
    url: "#",
    external: true,
  },
  {
    title: "Security Guide",
    description: "Best practices for account security",
    icon: Shield,
    url: "#",
    external: false,
  },
  {
    title: "Status Page",
    description: "Check system status and uptime",
    icon: Clock,
    url: "#",
    external: true,
  },
]

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [openSections, setOpenSections] = useState<string[]>(["Getting Started"])

  const toggleSection = (category: string) => {
    setOpenSections((prev) => (prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category]))
  }

  const filteredFAQ = faqData
    .map((category) => ({
      ...category,
      questions: category.questions.filter(
        (q) =>
          q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          q.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
          category.category.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((category) => category.questions.length > 0)

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <SidebarTrigger />
        <div className="flex items-center gap-2">
          <HelpCircle className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Help & Support</h1>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>How can we help you?</CardTitle>
          <CardDescription>Search our knowledge base or browse categories below</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search for help articles, features, or common issues..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* FAQ Section */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Find answers to common questions about using the platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {filteredFAQ.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No results found for "{searchQuery}"</div>
              ) : (
                filteredFAQ.map((category) => {
                  const IconComponent = category.icon
                  const isOpen = openSections.includes(category.category)

                  return (
                    <Collapsible
                      key={category.category}
                      open={isOpen}
                      onOpenChange={() => toggleSection(category.category)}
                    >
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full justify-between p-4 h-auto">
                          <div className="flex items-center gap-3">
                            <IconComponent className="h-5 w-5" />
                            <span className="font-medium">{category.category}</span>
                            <Badge variant="secondary">{category.questions.length}</Badge>
                          </div>
                          {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="space-y-3 px-4 pb-4">
                        {category.questions.map((faq, index) => (
                          <div key={index} className="border-l-2 border-muted pl-4">
                            <h4 className="font-medium mb-2">{faq.question}</h4>
                            <p className="text-sm text-muted-foreground leading-relaxed">{faq.answer}</p>
                          </div>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  )
                })
              )}
            </CardContent>
          </Card>
        </div>

        {/* Contact & Resources */}
        <div className="space-y-6">
          {/* Contact Support */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>Need personalized help? Reach out to our team</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {contactOptions.map((option, index) => {
                const IconComponent = option.icon
                return (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    <IconComponent className="h-5 w-5 mt-0.5 text-primary" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{option.title}</h4>
                        {option.badge && (
                          <Badge variant={option.badge === "Fastest" ? "default" : "secondary"} className="text-xs">
                            {option.badge}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{option.description}</p>
                      <p className="text-xs text-muted-foreground mb-3">{option.available}</p>
                      <Button size="sm" className="w-full">
                        {option.action}
                      </Button>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Resources */}
          <Card>
            <CardHeader>
              <CardTitle>Resources</CardTitle>
              <CardDescription>Additional documentation and guides</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {resources.map((resource, index) => {
                const IconComponent = resource.icon
                return (
                  <Button key={index} variant="ghost" className="w-full justify-start h-auto p-3" asChild>
                    <a href={resource.url} target={resource.external ? "_blank" : "_self"}>
                      <IconComponent className="h-4 w-4 mr-3" />
                      <div className="text-left flex-1">
                        <div className="font-medium">{resource.title}</div>
                        <div className="text-xs text-muted-foreground">{resource.description}</div>
                      </div>
                      {resource.external && <ExternalLink className="h-3 w-3 ml-2" />}
                    </a>
                  </Button>
                )
              })}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Support Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Avg. Response Time</span>
                <span className="font-medium">2.3 hours</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Resolution Rate</span>
                <span className="font-medium">98.5%</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Satisfaction Score</span>
                <span className="font-medium">4.9/5.0</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
