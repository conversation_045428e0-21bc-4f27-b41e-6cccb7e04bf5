import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function UpgradeToInstagramBusinessPage() {
  return (
    <div className="max-w-3xl mx-auto py-12">
      <Card>
        <CardHeader>
          <CardTitle>How to Upgrade to an Instagram Business Account</CardTitle>
        </CardHeader>
        <CardContent className="prose prose-lg">
          <p>
            Upgrading to an Instagram Business Account is free and gives you
            access to features that can help you grow your reach. Here's how to
            do it:
          </p>
          <ol>
            <li>
              <strong>Go to your profile</strong> and tap the menu icon in the
              top-right corner.
            </li>
            <li>
              Tap <strong>Settings</strong>.
            </li>
            <li>
              Tap <strong>Account</strong>.
            </li>
            <li>
              Tap <strong>Switch to Professional Account</strong>.
            </li>
            <li>
              Tap <strong>Continue</strong>.
            </li>
            <li>
              Select a <strong>Category</strong> for your business and tap{" "}
              <strong>Done</strong>.
            </li>
            <li>
              Tap <strong>OK</strong> to confirm.
            </li>
            <li>
              Tap <strong>Business</strong>.
            </li>
            <li>
              Tap <strong>Next</strong>.
            </li>
            <li>
              Add your contact details and tap <strong>Next</strong>. Or tap{" "}
              <strong>Don't use my contact info</strong> to skip this step.
            </li>
            <li>
              If you'd like, you can follow the steps to connect your Business
              Account to a Facebook Page associated with your business. This
              step is optional, but it will make it easier to use all of the
              features available for businesses across the Meta family of apps.
              At this time, only one Facebook Page can be connected to your
              Business Account.
            </li>
            <li>
              Tap <strong>X</strong> on the top right corner to return to your
              profile.
            </li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
