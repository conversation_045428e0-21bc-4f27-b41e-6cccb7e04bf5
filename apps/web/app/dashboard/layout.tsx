import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import {
  AppSidebar,
  SidebarWrapper,
} from "@/components/dashboard/layout/app-sidebar";
import { AuthProvider } from "@propelauth/nextjs/client";
import { SidebarInset } from "@/components/dashboard/layout/inset";
import { getUser } from "@propelauth/nextjs/server/app-router";
import NoTeam from "@/components/dashboard/layout/ErrorState/NoTeam";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Social Media Admin Dashboard",
  description: "Manage your social media analytics and connections",
  generator: "v0.dev",
};

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getUser();
  const orgs = user?.getOrgs();

  return (
    <AuthProvider authUrl={process.env.NEXT_PUBLIC_AUTH_URL!}>
      <SidebarWrapper>
        <AppSidebar />
        <SidebarInset>
          {!user?.activeOrgId ? <NoTeam /> : children}
        </SidebarInset>
      </SidebarWrapper>
      <Toaster />
    </AuthProvider>
  );
}
