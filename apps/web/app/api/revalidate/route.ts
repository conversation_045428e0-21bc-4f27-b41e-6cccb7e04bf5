import { revalidatePath, revalidateTag } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, projectId, connectionId, paths, tags } = body;

    // Verify the request has a valid secret (optional security measure)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.REVALIDATION_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Revalidate specific paths if provided
    if (paths && Array.isArray(paths)) {
      for (const path of paths) {
        revalidatePath(path);
        console.log(`Revalidated path: ${path}`);
      }
    }

    // Revalidate specific tags if provided
    if (tags && Array.isArray(tags)) {
      for (const tag of tags) {
        revalidateTag(tag);
        console.log(`Revalidated tag: ${tag}`);
      }
    }

    // Handle specific revalidation types
    if (type === 'connection' && projectId && connectionId) {
      // Revalidate connection-specific paths and tags
      const connectionPath = `/dashboard/projects/${projectId}/connections/${connectionId}`;
      const connectionsPath = `/dashboard/projects/${projectId}/connections`;
      
      revalidatePath(connectionPath);
      revalidatePath(connectionsPath);
      
      // Revalidate connection-related cache tags
      revalidateTag(`project_connection_${projectId}_${connectionId}`);
      revalidateTag(`project_connections_${projectId}`);
      revalidateTag(`count_connections_${projectId}`);
      
      console.log(`Revalidated connection: ${connectionId} in project: ${projectId}`);
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Revalidation completed',
      revalidated: {
        type,
        projectId,
        connectionId,
        paths,
        tags
      }
    });

  } catch (error) {
    console.error('Revalidation error:', error);
    return NextResponse.json(
      { error: 'Failed to revalidate' }, 
      { status: 500 }
    );
  }
}
