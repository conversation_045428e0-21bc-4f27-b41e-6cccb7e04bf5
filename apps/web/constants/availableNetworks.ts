import {
  SiFacebook,
  SiInstagram,
  SiTiktok,
  SiX,
  SiYoutube,
} from "@icons-pack/react-simple-icons";

// Available networks configuration
export const availableNetworks = [
  {
    name: "Facebook",
    slug: "facebook",
    icon: SiFacebook,
    description: "Pages, posts, and insights",
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  {
    name: "Instagram",
    slug: "instagram",
    icon: SiInstagram,
    description: "Posts, stories, and engagement",
    color: "text-pink-600",
    bgColor: "bg-pink-100",
  },
  {
    name: "YouTube",
    slug: "youtube",
    icon: SiYoutube,
    description: "Videos, comments, and analytics",
    color: "text-red-600",
    bgColor: "bg-red-100",
  },
  {
    name: "TikTok",
    slug: "tiktok",
    icon: <PERSON><PERSON>ik<PERSON>,
    description: "Videos, trends, and engagement",
    color: "text-black",
    bgColor: "bg-gray-100",
  },
];
