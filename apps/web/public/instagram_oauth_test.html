<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Instagram OAuth Test</title>
  </head>
  <body>
    <h1>Instagram OAuth Test</h1>
    <p>
      This page helps you test your Instagram Basic Display API OAuth setup.
    </p>
    <form id="oauthForm">
      <label for="clientId">Instagram App ID:</label><br />
      <input type="text" id="clientId" name="clientId" size="50" /><br /><br />
      <label for="redirectUri">Redirect URI:</label><br />
      <input
        type="text"
        id="redirectUri"
        name="redirectUri"
        size="50"
        value=""
      /><br /><br />
      <button type="submit">Start OAuth Flow</button>
    </form>

    <script>
      document.getElementById('oauthForm').addEventListener('submit', function(event) {
          event.preventDefault();
          const clientId = document.getElementById('clientId').value;
          const redirectUri = document.getElementById('redirectUri').value;
          const scope = 'user_profile,user_media';
          const responseType = 'code';

          if (!clientId || !redirectUri) {
              alert('Please provide both an App ID and a Redirect URI.');
              return;
          }

          const authUrl = `https://api.instagram.com/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${scope}&response_type=${responseType}`;

          window.location.href = authUrl;
      });

      // Check for OAuth response in URL
      window.onload = function() {
          const urlParams = new URLSearchParams(window.location.search);
          const code = urlParams.get('code');
          const error = urlParams.get('error');
          const errorReason = urlParams.get('error_reason');
          const errorDescription = urlParams.get('error_description');

          if (code) {
              document.body.innerHTML += \`<h2>Success!</h2><p>Authorization Code: <strong>${code}</strong></p>\`;
          } else if (error) {
              document.body.innerHTML += \`<h2>Error</h2><p><strong>Error:</strong> ${error}</p><p><strong>Reason:</strong> ${errorReason}</p><p><strong>Description:</strong> ${errorDescription}</p>\`;
          }
      };
    </script>
  </body>
</html>
