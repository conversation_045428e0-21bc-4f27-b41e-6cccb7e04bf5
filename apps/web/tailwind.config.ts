import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";
import defaultTheme from "tailwindcss/defaultTheme";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
          projects: {
            DEFAULT: "hsl(var(--card-projects-background))",
            foreground: "hsl(var(--card-projects-foreground))",
          },
          requests: {
            DEFAULT: "hsl(var(--card-requests-background))",
            foreground: "hsl(var(--card-requests-foreground))",
          },
          cost: {
            DEFAULT: "hsl(var(--card-cost-background))",
            foreground: "hsl(var(--card-cost-foreground))",
          },
          networks: {
            DEFAULT: "hsl(var(--card-networks-background))",
            foreground: "hsl(var(--card-networks-foreground))",
          },
          feeds: {
            DEFAULT: "hsl(var(--card-feeds-background))",
            foreground: "hsl(var(--card-feeds-foreground))",
          },
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    plugin(function ({ addUtilities, addComponents, e, config, theme }) {
      addComponents({
        ".btn": {
          fontWeight: "600",
          fontSize: "1rem",
          lineHeight: "1.4",
        },
        ".p1": {
          fontWeight: "400",
          fontSize: "1rem",
          lineHeight: "1.4",
        },
        ".p2": {
          fontWeight: "400",
          fontSize: "0.875rem",
          lineHeight: "1.4",
        },
        ".h1": {
          fontWeight: "800",
          fontSize: "2rem",
          lineHeight: "1.4",
          ".prose &": {
            marginTop: "2.5rem",
            marginBottom: "1rem",
            "@screen lg": {
              marginTop: "4rem",
              marginBottom: "1rem",
            },
          },
        },
        ".h2": {
          fontWeight: "600",
          fontSize: "1.5rem",
          lineHeight: "1.4",
          ".prose &": {
            marginTop: "2.5rem",
            marginBottom: "1rem",
            "@screen lg": {
              marginTop: "4rem",
              marginBottom: "1rem",
            },
          },
        },
        ".h3": {
          fontWeight: "500",
          fontSize: "1rem",
          lineHeight: "1.4",
          ".prose &": {
            marginTop: "2.5rem",
            marginBottom: "1rem",
            "@screen lg": {
              marginTop: "4rem",
              marginBottom: "1rem",
            },
          },
        },
        ".h4": {
          fontWeight: "600",
          fontSize: "0.875rem",
          lineHeight: "1.4",
          ".prose &": {
            marginTop: "2.5rem",
            marginBottom: "1rem",
            "@screen lg": {
              marginTop: "4rem",
              marginBottom: "1rem",
            },
          },
        },
      });
    }),
  ],
};
export default config;
