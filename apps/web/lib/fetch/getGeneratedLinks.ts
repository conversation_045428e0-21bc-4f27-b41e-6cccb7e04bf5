"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

export const getAllGeneratedLinks = async (
  projectId: string,
  connectionId: string
) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].connections[
    ":connectionId"
  ].links.$get(
    { param: { projectId, connectionId } },
    {
      init: {
        next: {
          revalidate: 60, // Daten alle 60 Sekunden revalidieren
          tags: ["links", projectId, connectionId], // Cache-Tag für gezielte Revalidierung
        },
      },
    }
  );
  const projects = await res.json();
  return projects;
};
