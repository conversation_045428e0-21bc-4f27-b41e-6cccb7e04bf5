"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";
import {
  getOneProjectConnection,
  getOneProjectConnectionReturnType,
} from "@socialfeed/api/src/hono/manage/project/connections/get";

export const getConnection = async (
  projectId: string,
  connectionId: string
) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].connections[
    ":connectionId"
  ].$get(
    { param: { projectId, connectionId } },
    {
      init: {
        next: {
          revalidate: 60, // Daten alle 60 Sekunden revalidieren
          tags: ["project_connection", projectId, connectionId], // Cache-Tag für gezielte Revalidierung
        },
      },
    }
  );
  const connection = await res.json();
  return connection as getOneProjectConnectionReturnType;
};
