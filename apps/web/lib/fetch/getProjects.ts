"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

export const getProjects = async () => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects.$get(undefined, {
    init: {
      next: {
        revalidate: 60, // Daten alle 60 Sekunden revalidieren
        tags: ["projects", orgId], // Cache-Tag für gezielte Revalidierung
      },
    },
  });
  const projects = await res.json();
  return projects;
};
