"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";

export interface FacebookPage {
  id: string;
  name: string;
  about?: string;
  fan_count?: number;
  followers_count?: number;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface FacebookPagesResponse {
  success: boolean;
  pages: FacebookPage[];
  currentPageId?: string | null;
}

export const getFacebookPages = async (
  projectId: string,
  connectionId: string
): Promise<FacebookPagesResponse> => {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) throw new Error("No access token");
    
    const user = await getUser();
    if (!user) throw new Error("No user");
    
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) throw new Error("No org id");
    
    const apiClient = createApiClient(accessToken, orgId);
    
    const res = await apiClient.manage.projects[":projectId"].connections[
      ":connectionId"
    ]["facebook-pages"].$get(
      { param: { projectId, connectionId } },
      {
        init: {
          next: {
            revalidate: 0, // Don't cache this data
          },
        },
      }
    );

    if (!res.ok) {
      throw new Error(`API request failed: ${res.status}`);
    }

    const data = await res.json();
    return data as FacebookPagesResponse;
  } catch (error) {
    console.error('Error fetching Facebook pages:', error);
    return {
      success: false,
      pages: [],
      currentPageId: null,
    };
  }
};
