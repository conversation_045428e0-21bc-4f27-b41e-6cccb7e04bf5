"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";
import { revalidateTag } from "next/cache";

export interface SelectPageResponse {
  success: boolean;
  message?: string;
  selectedPage?: {
    id: string;
    name: string;
    description?: string;
    followers_count?: number;
    profile_picture_url?: string;
  };
  error?: string;
}

export const selectFacebookPage = async (
  projectId: string,
  connectionId: string,
  pageId: string
): Promise<SelectPageResponse> => {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) throw new Error("No access token");
    
    const user = await getUser();
    if (!user) throw new Error("No user");
    
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) throw new Error("No org id");
    
    const apiClient = createApiClient(accessToken, orgId);
    
    const res = await apiClient.manage.projects[":projectId"].connections[
      ":connectionId"
    ]["select-page"].$post(
      { 
        param: { projectId, connectionId },
        json: { pageId }
      }
    );

    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed: ${res.status}`);
    }

    const data = await res.json();
    
    // Revalidate the connection cache since it has been updated
    revalidateTag("project_connection");
    revalidateTag(projectId);
    revalidateTag(connectionId);
    
    return data as SelectPageResponse;
  } catch (error) {
    console.error('Error selecting Facebook page:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to select Facebook page",
    };
  }
};
