import React from "react";
import {
  hasPermission,
  NoActiveOrganizationError,
  NoOrganizationFoundError,
  WrongOrganizationError,
  WrongPermissionsError,
} from "./protectRoute";
import { redirect } from "next/navigation";

const ProtectedRoute = async ({
  organizationId,
  permissions,
  children,
}: {
  organizationId?: string;
  permissions?: string[];
  children: React.ReactNode;
}) => {
  try {
    const user = await hasPermission({ organizationId, permissions });
  } catch (e) {
    if (e instanceof NoOrganizationFoundError) {
      console.log("NO ORGANIZATIONS FOUND");
      redirect(`${process.env.NEXT_PUBLIC_AUTH_URL}/create_org`);
      return <div>No organization found</div>;
    }
    if (e instanceof NoActiveOrganizationError) {
      console.log("NO ACTIVE ORGANIZATION FOUND");
      redirect(`/dashboard/team/select`);
      return <div>No active organization</div>;
    }
    if (e instanceof WrongOrganizationError) {
      console.log("WRONG ORGANIZATION FOUND");
      redirect(`/dashboard/team/select`);
      return <div>Wrong organization</div>;
    }
    if (e instanceof WrongPermissionsError) {
      console.log("NO PERMISSIONS");
      redirect(`/dashboard/unauthorized`);
      return <div>Wrong permissions</div>;
    }
  }
  return children;
};

export default ProtectedRoute;
