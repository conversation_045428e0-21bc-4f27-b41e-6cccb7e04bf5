import { getUserOrRedirect } from "@propelauth/nextjs/server/app-router";

export const hasPermission = async ({
  organizationId,
  permissions,
}: {
  organizationId?: string;
  permissions?: string[];
}) => {
  const user = await getUserOrRedirect();
  const org = user.getActiveOrg();
  if (org?.orgId === undefined) {
    throw new NoActiveOrganizationError();
  }
  if (organizationId && organizationId !== org?.orgId) {
    if (user.getOrgs().length === 0) {
      throw new NoOrganizationFoundError();
    }
    throw new WrongOrganizationError();
  }

  if (permissions && !permissions.some((p) => org.hasPermission(p))) {
    throw new WrongPermissionsError();
  }
  return user;
};

export class NoActiveOrganizationError extends Error {
  constructor() {
    super("No active organization");
  }
}

export class NoOrganizationFoundError extends Error {
  constructor() {
    super("No organization found");
  }
}

export class WrongOrganizationError extends Error {
  constructor() {
    super("Wrong organization");
  }
}

export class WrongPermissionsError extends Error {
  constructor() {
    super("Wrong permissions");
  }
}
