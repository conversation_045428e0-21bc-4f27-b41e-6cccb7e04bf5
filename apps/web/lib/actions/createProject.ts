"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

// Type for the response from creating a project
export type CreateProjectResponse = {
  id: string | null;
};

export const createProject = async (
  name: string,
  description: string
): Promise<CreateProjectResponse> => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects.$post({
    json: {
      name,
      description,
    },
  });
  const data = await res.json();
  revalidatePath(`/dashboard/projects`);
  return data as CreateProjectResponse;
};
