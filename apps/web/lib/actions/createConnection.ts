"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { platformConnections } from "@socialfeed/api/src/db/schema";

export const createConnection = async ({
  name,
  projectId,
  platform,
  generateLink,
}: {
  name: string;
  projectId: string;
  platform: (typeof platformConnections.platform.enumValues)[number];
  generateLink?: boolean;
}) => {
  if (!projectId) throw new Error("No project id");
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].connections.$post({
    param: { projectId },
    json: {
      name,
      platform,
      generateLink,
    },
  });
  console.log("RES", res);
  const data = await res.json();
  revalidatePath(`/dashboard/projects/${projectId}/connections`);
  return data;
};
