"use server";
import { createApiClient } from "@/lib/api";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { revalidatePath } from "next/cache";

export async function syncPosts(
  projectId: string,
  connectionId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) {
      return { success: false, error: "Unauthorized" };
    }
    const user = await getUser();
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) {
      return { success: false, error: "No active organization" };
    }

    const hono = createApiClient(accessToken, orgId);

    // Get connection details to check lastSyncedAt
    const connectionRes = await hono.manage.projects[":projectId"].connections[
      ":connectionId"
    ].$get({
      param: { projectId, connectionId },
    });

    if (!connectionRes.ok) {
      return { success: false, error: "Failed to get connection details" };
    }

    const connection = await connectionRes.json();
    const lastSyncedAt = connection.lastSyncedAt;
    const now = Date.now();

    if (lastSyncedAt && now - new Date(lastSyncedAt).getTime() < 60000) {
      return {
        success: false,
        error: "You can only sync posts once per minute.",
      };
    }

    console.log("Syncing posts for connection:", connectionId);

    const response = await hono.manage.projects[":projectId"].connections[
      ":connectionId"
    ].sync.$post({
      param: { projectId, connectionId },
    });

    if (response.ok) {
      revalidatePath(
        `/dashboard/projects/${projectId}/connections/${connectionId}`
      );
      return { success: true };
    } else {
      const errorData = await response.json();
      if ("error" in errorData) {
        return { success: false, error: errorData.error };
      }
      return { success: false, error: "An unknown error occurred." };
    }
  } catch (error) {
    return { success: false, error: "An unexpected error occurred." };
  }
}
