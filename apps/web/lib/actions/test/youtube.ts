"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const testYoutube = async ({
  connectionId,
  projectId,
}: {
  connectionId: string;
  projectId: string;
}) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].connections[
    ":connectionId"
  ].test.$post({
    param: {
      projectId,
      connectionId,
    },
  });

  if (!res.ok) {
    const errorData = await res.text();
    console.error("Test connection error:", res.status, errorData);
    throw new Error(`Failed to test connection: ${res.status} ${errorData}`);
  }

  const data = await res.json();
  revalidatePath(
    `/dashboard/projects/${projectId}/connections/${connectionId}`
  );
  return data;
};
