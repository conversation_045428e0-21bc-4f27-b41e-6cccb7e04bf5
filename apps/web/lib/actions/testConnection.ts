"use server";
import { createApiClient } from "@/lib/api";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";

export interface TestConnectionResult {
  success: boolean;
  message?: string;
  error?: string;
  status: string;
  platform?: string;
  accountName?: string;
  lastChecked?: string;
}

export const testConnection = async (
  projectId: string,
  connectionId: string
): Promise<TestConnectionResult> => {
  if (!projectId) throw new Error("No project id");
  if (!connectionId) throw new Error("No connection id");

  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");

  const user = await getUser();
  if (!user) throw new Error("No user");

  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");

  const apiClient = createApiClient(accessToken, orgId);

  try {
    const res = await apiClient.manage.projects[":projectId"].connections[
      ":connectionId"
    ].test.$post({
      param: { projectId, connectionId },
    });

    if (!res.ok) {
      const errorData = await res.text();
      throw new Error(`API request failed: ${res.status} ${errorData}`);
    }

    const result = await res.json();
    console.log("TEST CONNECTION RESULT", result);
    return result as TestConnectionResult;
  } catch (error) {
    console.error("Test connection error:", error);
    throw error;
  }
};
