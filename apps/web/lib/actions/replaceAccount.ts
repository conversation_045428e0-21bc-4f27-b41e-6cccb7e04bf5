"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { getOneProjectConnectionReturnType } from "@socialfeed/api/src/hono/manage/project/connections/get";

export interface ReplaceAccountResult {
  success: boolean;
  redirectUrl?: string;
  error?: string;
}

export const replaceAccount = async (
  projectId: string,
  connectionId: string
): Promise<ReplaceAccountResult> => {
  if (!projectId) {
    return {
      success: false,
      error: "No project id provided",
    };
  }

  if (!connectionId) {
    return {
      success: false,
      error: "No connection id provided",
    };
  }

  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) {
      return {
        success: false,
        error: "No access token available",
      };
    }

    const user = await getUser();
    if (!user) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) {
      return {
        success: false,
        error: "No organization context",
      };
    }

    const apiClient = createApiClient(accessToken, orgId);
    // First, get the connection details to know which platform
    const connectionRes = await apiClient.manage.projects[
      ":projectId"
    ].connections[":connectionId"].$get({
      param: { projectId, connectionId },
    });

    if (!connectionRes.ok) {
      return {
        success: false,
        error: `Failed to get connection: ${connectionRes.status}`,
      };
    }

    const connection =
      (await connectionRes.json()) as getOneProjectConnectionReturnType;

    // Determine the OAuth URL based on platform
    let oauthUrl: string;

    switch (connection.platform) {
      case "youtube":
        oauthUrl = `${process.env.NEXT_PUBLIC_API_URL}/oauth/youtube/authorize?connection_id=${connectionId}&type=replace`;
        break;
      case "instagram":
      case "instagram_business":
      case "instagram_with_facebook":
      case "facebook":
        oauthUrl = `${process.env.NEXT_PUBLIC_API_URL}/oauth/meta/authorize?connection_id=${connectionId}&type=replace`;
        break;
      default:
        return {
          success: false,
          error: `Platform ${connection.platform} not supported for account replacement`,
        };
    }

    // Return the OAuth URL for client-side redirect
    return {
      success: true,
      redirectUrl: oauthUrl,
    };
  } catch (error) {
    console.error("Replace account error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to replace account",
    };
  }
};
