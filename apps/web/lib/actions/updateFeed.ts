"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const updateFeed = async ({
  name,
  projectId,
  feedId,
  isActive,
  description,
  connections,
}: {
  projectId: string;
  feedId: string;
  isActive?: boolean | null;
  name?: string | null;
  description?: string | null;
  connections?: string[] | null;
}) => {
  if (!projectId) throw new Error("No project id");
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].feeds[
    ":feedId"
  ].$post({
    param: { projectId, feedId },
    json: {
      name,
      description,
      connections,
      isActive,
    },
  });
  const data = await res.json();
  revalidatePath(`/dashboard/projects/${projectId}/feeds/${feedId}`);
  return data;
};
