"use server";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

export const createAPIKey = async ({
  projectId,
  feedId,
  name,
  expiresAt,
}: {
  projectId: string;
  feedId: string;
  name: string;
  expiresAt?: Date;
}) => {
  if (!projectId) throw new Error("No project id");
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  const res = await apiClient.manage.projects[":projectId"].feeds[
    ":feedId"
  ].apiKeys.$post({
    param: { projectId, feedId },
    json: {
      name,
      expiresAt,
    },
  });
  console.log("RES", res);
  const data = await res.json();
  revalidatePath(`/dashboard/projects/${projectId}/feeds/${feedId}`);
  return data;
};
