"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import { revalidatePath } from "next/cache";

export interface DisconnectConnectionResult {
  success: boolean;
  message?: string;
  error?: string;
  platform?: string;
}

export const disconnectConnection = async (
  projectId: string,
  connectionId: string
): Promise<DisconnectConnectionResult> => {
  if (!projectId) throw new Error("No project id");
  if (!connectionId) throw new Error("No connection id");
  
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  
  const user = await getUser();
  if (!user) throw new Error("No user");
  
  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");
  
  const apiClient = createApiClient(accessToken, orgId);
  
  try {
    const res = await apiClient.manage.projects[":projectId"].connections[":connectionId"].disconnect.$post({
      param: { projectId, connectionId },
    });
    
    if (!res.ok) {
      const errorData = await res.text();
      throw new Error(`API request failed: ${res.status} ${errorData}`);
    }
    
    const result = await res.json();
    
    // Revalidate the connection page to reflect the changes
    revalidatePath(`/dashboard/projects/${projectId}/connections/${connectionId}`);
    revalidatePath(`/dashboard/projects/${projectId}/connections`);
    
    return result as DisconnectConnectionResult;
  } catch (error) {
    console.error("Disconnect connection error:", error);
    throw error;
  }
};
