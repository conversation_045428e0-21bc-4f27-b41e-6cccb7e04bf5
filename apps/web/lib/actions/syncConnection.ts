"use server";

import { createApiClient } from "@/lib/api";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";

interface SyncConnectionResult {
  success: boolean;
  posts?: any[];
  error?: string;
}

export const syncConnection = async (
  projectId: string,
  connectionId: string
): Promise<SyncConnectionResult> => {
  if (!projectId) throw new Error("No project id");
  if (!connectionId) throw new Error("No connection id");

  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");

  const user = await getUser();
  if (!user) throw new Error("No user");

  const orgId = user?.getActiveOrg()?.orgId;
  if (!orgId) throw new Error("No org id");

  const apiClient = createApiClient(accessToken, orgId);

  try {
    const res = await apiClient.manage.projects[":projectId"].connections[
      ":connectionId"
    ].sync.$post({
      param: { projectId, connectionId },
    });

    if (!res.ok) {
      const errorData = await res.text();
      throw new Error(`API request failed: ${res.status} ${errorData}`);
    }

    const result = await res.json();
    return result as SyncConnectionResult;
  } catch (error) {
    console.error("Sync connection error:", error);
    throw error;
  }
};
