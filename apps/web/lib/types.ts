// Social media post types

export interface Author {
  id: string;
  name: string;
  username: string;
  profileImageUrl?: string;
  verified?: boolean;
}

export interface MediaItem {
  type: "image" | "video";
  url: string;
  thumbnailUrl?: string;
  altText?: string;
  width?: number;
  height?: number;
}

export interface LinkPreview {
  url: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  domain: string;
}

export interface PostStats {
  likes: number;
  comments: number;
  shares: number;
  views?: number;
}

export interface SocialMediaPost {
  id: string;
  platform: string;
  content: string;
  author: Author;
  timestamp: string;
  postUrl: string;
  media?: MediaItem[];
  linkPreview?: LinkPreview;
  stats: PostStats;
}
