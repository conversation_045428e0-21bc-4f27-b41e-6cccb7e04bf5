import type React from "react";

import {
  MessageS<PERSON>re,
  Repeat2,
  Heart,
  Share2,
  MoreHorizontal,
  Rss,
  CheckCircle,
  PlayCircle,
  Linkedin,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import type { SocialMediaPost, MediaItem } from "@/lib/types";
import {
  SiFacebook,
  SiInstagram,
  SiTiktok,
  SiX,
  SiYoutube,
} from "@icons-pack/react-simple-icons";

const PLATFORM_ICONS: { [key: string]: React.ElementType } = {
  Twitter: SiX,
  Facebook: SiFacebook,
  Instagram: SiInstagram,
  YouTube: SiYoutube,
  LinkedIn: <PERSON>edin,
  RSS: Rss,
  TikTok: <PERSON>Tiktok,
};

export function SocialMediaPostCard({ post }: { post: SocialMediaPost }) {
  const PlatformIcon = PLATFORM_ICONS[post.platform] || Rss;

  const formatCount = (count: number) => {
    if (count >= 1000000) return (count / 1000000).toFixed(1) + "M";
    if (count >= 1000) return (count / 1000).toFixed(1) + "K";
    return count.toString();
  };

  return (
    <Card className="w-full max-w-xl overflow-hidden">
      <CardHeader className="flex flex-row items-start space-x-3 p-4">
        <Avatar className="h-10 w-10 border">
          <AvatarImage
            src={post.author.profileImageUrl || "/placeholder.svg"}
            alt={post.author.name}
          />
          <AvatarFallback>
            {post.author.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <div className="flex items-center space-x-1">
            <span className="font-semibold">{post.author.name}</span>
            {post.author.verified && (
              <CheckCircle className="h-4 w-4 text-blue-500" />
            )}
            <span className="text-sm text-muted-foreground">
              @{post.author.username}
            </span>
            <span className="text-sm text-muted-foreground">·</span>
            <a
              href={post.postUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-muted-foreground hover:underline"
            >
              {new Date(post.timestamp).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
              })}
            </a>
          </div>
          <div className="text-sm text-muted-foreground flex items-center">
            <PlatformIcon className="h-4 w-4 mr-1" /> {post.platform}
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>View on {post.platform}</DropdownMenuItem>
            <DropdownMenuItem>Embed Post</DropdownMenuItem>
            <DropdownMenuItem>Report Post</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="px-4 pb-2 space-y-3">
        <p className="text-sm whitespace-pre-wrap">{post.content}</p>
        {post.media && post.media.length > 0 && (
          <div
            className={`grid gap-1 ${
              post.media.length > 1 ? "grid-cols-2" : "grid-cols-1"
            } rounded-lg overflow-hidden border`}
          >
            {post.media
              .slice(0, 4)
              .map((mediaItem: MediaItem, index: number) => (
                <div
                  key={index}
                  className={`relative aspect-video ${
                    post.media?.length === 3 && index === 0 ? "col-span-2" : ""
                  } ${post.media?.length === 1 ? "aspect-[16/9]" : "aspect-square"}`}
                >
                  {mediaItem.type === "image" && (
                    <Image
                      src={mediaItem.url || "/placeholder.svg"}
                      alt={mediaItem.altText || `Post media ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  )}
                  {mediaItem.type === "video" && (
                    <>
                      <Image
                        src={
                          mediaItem.thumbnailUrl ||
                          "/placeholder.svg?width=300&height=200&text=Video"
                        }
                        alt={
                          mediaItem.altText || `Video thumbnail ${index + 1}`
                        }
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                        <PlayCircle className="h-12 w-12 text-white" />
                      </div>
                    </>
                  )}
                </div>
              ))}
            {post.media.length > 4 && (
              <div className="aspect-square flex items-center justify-center bg-muted text-muted-foreground">
                +{post.media.length - 4}
              </div>
            )}
          </div>
        )}
        {post.linkPreview && (
          <a
            href={post.linkPreview.url}
            target="_blank"
            rel="noopener noreferrer"
            className="block border rounded-lg overflow-hidden hover:bg-muted/50 transition-colors"
          >
            {post.linkPreview.imageUrl && (
              <div className="relative aspect-video">
                <Image
                  src={post.linkPreview.imageUrl || "/placeholder.svg"}
                  alt={post.linkPreview.title || "Link preview"}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="p-3">
              <p className="text-xs text-muted-foreground uppercase">
                {post.linkPreview.domain}
              </p>
              <p className="text-sm font-medium truncate">
                {post.linkPreview.title}
              </p>
              {post.linkPreview.description && (
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {post.linkPreview.description}
                </p>
              )}
            </div>
          </a>
        )}
      </CardContent>

      <CardFooter className="flex justify-around items-center p-2 border-t">
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-blue-500"
        >
          <MessageSquare className="h-4 w-4 mr-1" />{" "}
          {formatCount(post.stats.comments)}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-green-500"
        >
          <Repeat2 className="h-4 w-4 mr-1" /> {formatCount(post.stats.shares)}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-red-500"
        >
          <Heart className="h-4 w-4 mr-1" /> {formatCount(post.stats.likes)}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-sky-500"
        >
          <Share2 className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}
