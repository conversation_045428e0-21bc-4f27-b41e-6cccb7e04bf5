"use client";
import React from "react";
import { Button } from "../ui/button";
import { toast } from "../ui/use-toast";
import { Copy } from "lucide-react";

const CopyButton = ({
  value,
  toast: toastOptions,
  children,
  ...props
}: {
  value: string;
  toast?: { title?: string; description?: string };
  children?: React.ReactNode;
} & React.ComponentProps<typeof Button>) => {
  const copyToClipboard = (value?: string) => {
    if (!value) return;
    navigator.clipboard.writeText(value);
    toastOptions &&
      toast({
        title: toastOptions?.title,
        description: toastOptions?.description,
      });
  };
  return (
    <Button
      size="icon"
      variant="outline"
      onClick={() => copyToClipboard(value)}
      {...props}
    >
      {children || <Copy className="h-4 w-4" />}
    </Button>
  );
};

export default CopyButton;
