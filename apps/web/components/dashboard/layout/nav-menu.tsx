"use client";
import React from "react";
import { Building, DollarSign, Settings, Users } from "lucide-react";

import Link from "next/link";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { usePathname, useRouter } from "next/navigation";

// Navigation items
const navigationItems = [
  {
    title: "Projects",
    url: "/dashboard/projects",
    icon: Building,
    description: "Manage your social media projects",
  },
  {
    title: "Team",
    url: "/dashboard/team",
    icon: Users,
    description: "Manage team members and permissions",
  },
  {
    title: "Billing",
    url: "/dashboard/billing",
    icon: DollarSign,
    description: "View billing and usage information",
  },
  {
    title: "Settings",
    url: "/dashboard/settings",
    icon: Settings,
    description: "Account and application settings",
  },
];

const SidebarNavMenu = () => {
  const pathname = usePathname();

  return (
    <div className="px-0.5 flex flex-col gap-7 justify-center relative w-16">
      {navigationItems.map((item) => {
        const IconComponent = item.icon;
        const isActive = pathname.startsWith(item.url);
        return (
          <Tooltip key={item.title}>
            <TooltipTrigger asChild>
              <Link
                key={item.title}
                href={item.url}
                data-active={isActive}
                className="flex relative group/button items-center justify-center rounded-md hover:bg-[#D0F100]/30 data-[active=true]:bg-[#D0F100]/30 w-full aspect-square"
              >
                <IconComponent className="size-[55%] group-data-[active=true]/button:text-[#D0F100] group-hover/button:text-[#D0F100] text-sidebar-foreground" />
                <span className="sr-only">{item.title}</span>
              </Link>
            </TooltipTrigger>
            <TooltipContent className="" side="right">
              <p>{item.title}</p>
            </TooltipContent>
          </Tooltip>
        );
      })}
    </div>
  );
};

export default SidebarNavMenu;
