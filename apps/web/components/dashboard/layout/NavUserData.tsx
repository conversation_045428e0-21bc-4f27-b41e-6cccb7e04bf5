import React from "react";
import { getUser } from "@propelauth/nextjs/server/app-router";
import { NavUser } from "./nav-user";

const NavUserData = async () => {
  const user = await getUser();

  const nArr = [];
  if (user?.firstName) {
    nArr.push(user.firstName);
  }
  if (user?.lastName) {
    nArr.push(user.lastName);
  }

  return (
    <NavUser
      user={{
        name: nArr.join(" ") ?? null,
        email: user?.email ?? "",
        avatar: "",
      }}
    />
  );
};

export default NavUserData;
