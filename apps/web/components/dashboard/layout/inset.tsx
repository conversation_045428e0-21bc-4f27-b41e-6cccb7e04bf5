"use client";

import * as React from "react";

export const SidebarInset: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const contentRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const content = contentRef.current;
    if (!content) return;

    // Set body height to match content height to show scrollbar
    const updateBodyHeight = () => {
      const contentHeight = content.scrollHeight;
      document.body.style.height = `${contentHeight}px`;
    };

    // Initial height update
    updateBodyHeight();

    // Update on resize
    const resizeObserver = new ResizeObserver(updateBodyHeight);
    resizeObserver.observe(content);

    // Sync scroll positions
    const handleBodyScroll = () => {
      if (content) {
        content.scrollTop = window.scrollY;
      }
    };

    const handleContentScroll = () => {
      window.scrollTo(0, content.scrollTop);
    };

    window.addEventListener("scroll", handleBodyScroll);
    content.addEventListener("scroll", handleContentScroll);

    return () => {
      window.removeEventListener("scroll", handleBodyScroll);
      content.removeEventListener("scroll", handleContentScroll);
      resizeObserver.disconnect();
      document.body.style.height = "";
    };
  }, []);

  return (
    <div className="max-h-[calc(100dvh-3.5rem)] bg-background rounded-3xl w-full overflow-hidden">
      <div
        ref={contentRef}
        className="h-full w-full overflow-auto scrollbar-none"
      >
        {children}
      </div>
    </div>
  );
};
