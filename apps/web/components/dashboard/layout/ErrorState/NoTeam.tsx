"use client";
// A Page to select an organization
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useUser } from "@propelauth/nextjs/client";

const NoTeam = ({ redirect }: { redirect?: string }) => {
  const router = useRouter();

  const { user, setActiveOrg } = useUser();
  const orgs = user?.getOrgs();

  const handleSelectOrg = async (orgId: string) => {
    const newUser = await setActiveOrg(orgId);

    if (newUser?.activeOrgId === orgId) {
      toast({
        title: "Organization selected",
      });
      //router.push(redirect ?? "/dashboard/projects");
    } else {
      toast({
        title: "Error",
        description: "Failed to select organization",
        variant: "destructive",
      });
    }
  };

  // Display a list of all organizations to select one. Also a button to create a new one.
  return (
    <div className="flex flex-1 flex-col gap-4 p-4 w-full h-full">
      <div className="flex-1 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Select Your Team</CardTitle>
            <CardDescription>
              Select the team you want to work with or create a new one.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {!orgs && (
                // Loading state
                <div className="flex p-3 rounded-lg">
                  <span>Loading...</span>
                </div>
              )}
              {orgs?.map((org) => (
                <div
                  key={org.orgId}
                  className="flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50"
                  onClick={() => handleSelectOrg(org.orgId)}
                >
                  <div className="flex items-center space-x-3">
                    <span>{org.orgName}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={() => {
                window.location.href = `${process.env.NEXT_PUBLIC_AUTH_URL}/create_org`;
              }}
            >
              Create Organization
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default NoTeam;
