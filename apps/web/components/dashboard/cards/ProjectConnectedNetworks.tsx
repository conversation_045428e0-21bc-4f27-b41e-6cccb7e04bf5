import React from "react";
import { TrendingUp } from "lucide-react";
import AnalyticsCard from "./AnalyticsCard";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";

const getConnectedNetworks = async ({ projectId }: { projectId: string }) => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const org = user.getActiveOrg();
  const orgId = org?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  // --- DER MAGISCHE TEIL: Typsicherer API-Aufruf ---
  const res = await apiClient.manage.count.projects[
    ":projectId"
  ].connections.$get(
    {
      param: {
        projectId,
      },
    },
    {
      init: {
        next: {
          revalidate: 60, // Daten alle 60 Sekunden revalidieren
          tags: ["count", "connections", projectId], // Cache-Tag für gezielte Revalidierung
        },
      },
    }
  );
  const data = await res.json();
  if (!data) {
    return {
      count: 0,
      countActive: 0,
      countConnected: 0,
    };
  }
  return data as {
    count: number;
    countActive: number;
    countConnected: number;
  };
};

const ProjectConnectedNetworks = async ({
  projectId,
}: {
  projectId: string;
}) => {
  const countedNetworks = await getConnectedNetworks({ projectId });
  return (
    <AnalyticsCard
      className="text-card-networks-foreground bg-card-networks"
      icon={<TrendingUp className="h-9 w-9 absolute right-5 top-4" />}
      title="Connected Networks"
      value={countedNetworks.count}
      description={`Total connections`}
    />
  );
};

export default ProjectConnectedNetworks;
