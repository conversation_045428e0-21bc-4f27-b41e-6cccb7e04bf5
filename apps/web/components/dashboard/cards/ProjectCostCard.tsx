import React from "react";
import { DollarSign } from "lucide-react";
import AnalyticsCard from "./AnalyticsCard";

const ProjectCostCard = () => {
  return (
    <AnalyticsCard
      className="text-card-cost-foreground bg-card-cost"
      icon={<DollarSign className="h-9 w-9 absolute right-5 top-4" />}
      title="Total Cost"
      value={`$${(0).toFixed(2)}`}
      description={`This month (updated hourly)`}
    />
  );
};

export default ProjectCostCard;
