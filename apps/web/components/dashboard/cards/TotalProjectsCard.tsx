import React from "react";
import { Building } from "lucide-react";
import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "@/lib/api";
import AnalyticsCard from "./AnalyticsCard";

const getProjectCount = async () => {
  const accessToken = await getAccessTokenAsync();
  if (!accessToken) throw new Error("No access token");
  const user = await getUser();
  if (!user) throw new Error("No user");
  const org = user.getActiveOrg();
  const orgId = org?.orgId;
  if (!orgId) throw new Error("No org id");
  const apiClient = createApiClient(accessToken, orgId);
  // --- DER MAGISCHE TEIL: Typsicherer API-Aufruf ---
  const res = await apiClient.manage.count.projects.$get(undefined, {
    init: {
      next: {
        revalidate: 60, // Daten alle 60 Sekunden revalidieren
        tags: ["count", "projects"], // Cache-Tag für gezielte Revalidierung
      },
    },
  });
  const data = await res.json();
  return data;
};

const TotalProjectsCard: React.FC = async () => {
  const count = await getProjectCount();
  return (
    <AnalyticsCard
      className="text-card-projects-foreground bg-card-projects"
      icon={<Building className="h-9 w-9 absolute right-5 top-4" />}
      title="Total Projects"
      value={count.projects}
      description={`${count.activeProjects} active`}
    />
  );
};

export default TotalProjectsCard;
