import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "../../ui/card";
import { cn } from "@/lib/utils";

const AnalyticsCard: React.FC<{
  className?: string;
  icon: React.ReactNode;
  title: string;
  value: string | number;
  description: string;
}> = ({ icon, className, title, description, value }) => {
  return (
    <Card className={cn("text-card-foreground bg-card", className)}>
      <CardHeader className="flex flex-row relative items-center justify-between space-y-0 pb-2">
        <CardTitle className="h4">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="h1">{value}</div>
        <p className="p2">{description}</p>
      </CardContent>
    </Card>
  );
};

export default AnalyticsCard;
