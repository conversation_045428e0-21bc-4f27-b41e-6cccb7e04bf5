import React from "react";
import { BarChart3 } from "lucide-react";
import AnalyticsCard from "./AnalyticsCard";

const ProjectRequestsCard = () => {
  return (
    <AnalyticsCard
      className="text-card-requests-foreground bg-card-requests"
      icon={<BarChart3 className="h-9 w-9 absolute right-5 top-4" />}
      title="Total Requests"
      value={0}
      description={`This month (updated hourly)`}
    />
  );
};

export default ProjectRequestsCard;
