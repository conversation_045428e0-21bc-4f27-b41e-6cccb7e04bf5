"use client";

import type React from "react";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Link2,
  Rss,
  Puzzle,
  SettingsIcon,
  Menu,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

const getProjectNavigationItems = (projectId: string) => [
  {
    name: "Overview",
    href: `/dashboard/projects/${projectId}/overview`,
    icon: LayoutDashboard,
  },
  {
    name: "Connections",
    href: `/dashboard/projects/${projectId}/connections`,
    icon: Link2,
  },
  { name: "Feeds", href: `/dashboard/projects/${projectId}/feeds`, icon: Rss },
  {
    name: "Components",
    href: `/dashboard/projects/${projectId}/components`,
    icon: Puzzle,
  },
  {
    name: "Settings",
    href: `/dashboard/projects/${projectId}/settings`,
    icon: SettingsIcon,
  },
];

export function ProjectHorizontalNav() {
  const params = useParams();
  const pathname = usePathname();
  const projectId = params.id as string;

  if (!projectId) return null; // Should not happen if routing is correct

  const navigationItems = getProjectNavigationItems(projectId);

  const isActive = (href: string) => {
    // For Overview, exact match. For others, startsWith.
    if (href === `/project/${projectId}`) return pathname === href;
    return pathname.startsWith(href);
  };

  return (
    <div className="bg-background sticky top-0 z-30">
      {" "}
      {/* Ensure z-index is appropriate */}
      {/* Desktop Navigation */}
      <div className="hidden md:flex h-12 items-center px-6">
        <NavigationMenu>
          <NavigationMenuList>
            {navigationItems.map((item) => (
              <NavigationMenuItem key={item.name}>
                <Link href={item.href} legacyBehavior passHref>
                  <NavigationMenuLink
                    className={cn(
                      navigationMenuTriggerStyle(),
                      "text-sm h-9 px-3 py-2", // Adjusted padding for consistency
                      isActive(item.href)
                        ? "bg-primary text-primary-foreground"
                        : "bg-card"
                    )}
                  >
                    <item.icon className="h-4 w-4 mr-2" />
                    {item.name}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>
      </div>
      {/* Mobile Navigation */}
      <div className="md:hidden flex h-12 items-center px-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Menu className="h-4 w-4 mr-2" />
              Menu
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            {navigationItems.map((item) => (
              <DropdownMenuItem
                key={item.name}
                asChild
                className={cn(
                  isActive(item.href) ? "bg-accent text-accent-foreground" : ""
                )}
              >
                <Link href={item.href}>
                  <item.icon className="h-4 w-4 mr-2" />
                  {item.name}
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        <span className="ml-4 text-sm font-medium text-muted-foreground truncate">
          {navigationItems.find((item) => isActive(item.href))?.name ||
            "Overview"}
        </span>
      </div>
    </div>
  );
}
