"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Permission {
  name: string;
  description: string;
  required: boolean;
}

interface BaseOAuthConnectorProps {
  platform: string;
  platformName: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  permissions: Permission[];
  onConnect: () => Promise<void>;
  isConnecting: boolean;
  connectionId: string;
  token?: string;
  color?: string;
}

export function BaseOAuthConnector({
  platform,
  platformName,
  icon: Icon,
  description,
  permissions,
  onConnect,
  isConnecting,
  connectionId,
  token,
  color = "bg-blue-600",
  children,
}: React.PropsWithChildren<BaseOAuthConnectorProps>) {
  const { toast } = useToast();

  const handleConnect = async () => {
    if (platform === "instagram") {
      window.location.href = `/dashboard/connect/instagram?connection_id=${connectionId}${token ? `&token=${token}` : ""}`;
    } else {
      try {
        await onConnect();
      } catch (error) {
        console.error(`${platformName} OAuth error:`, error);
        toast({
          title: "Connection Failed",
          description: `Failed to connect to ${platformName}. Please try again.`,
          variant: "destructive",
        });
      }
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Platform Header */}
      <div className="text-center space-y-4">
        <div
          className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${color} text-white`}
        >
          <Icon className="w-8 h-8" />
        </div>
        <div>
          <h1 className="text-3xl font-bold">Connect to {platformName}</h1>
          <p className="text-muted-foreground mt-2">{description}</p>
        </div>
      </div>

      {/* Connection Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon className="w-5 h-5" />
            {platformName} Connection
          </CardTitle>
          <CardDescription>
            Authorize access to your {platformName} account to start collecting
            data.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Permissions */}
          <div>
            <h3 className="font-semibold mb-3">Required Permissions</h3>
            <div className="space-y-2">
              {permissions.map((permission, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg"
                >
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{permission.name}</span>
                      {permission.required && (
                        <Badge variant="secondary" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {permission.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Security Notice */}
          <div className="flex items-start gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900">Secure Connection</h4>
              <p className="text-sm text-blue-700 mt-1">
                Your credentials are encrypted and stored securely. We only
                access the data you explicitly authorize.
              </p>
            </div>
          </div>

          {children ? (
            children
          ) : (
            <Button
              onClick={handleConnect}
              disabled={isConnecting}
              className={`w-full ${color} hover:opacity-90`}
              size="lg"
            >
              {isConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting to {platformName}...
                </>
              ) : (
                <>
                  <Icon className="w-4 h-4 mr-2" />
                  Connect to {platformName}
                </>
              )}
            </Button>
          )}

          <p className="text-xs text-muted-foreground text-center">
            By connecting, you agree to our terms of service and privacy policy.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
