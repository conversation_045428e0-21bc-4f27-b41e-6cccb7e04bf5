"use client";

import { useState } from "react";
import { Instagram } from "lucide-react";
import { BaseOAuthConnector } from "./base-oauth-connector";

interface InstagramBasicConnectorProps {
  connectionId: string;
  token?: string;
}

const permissions = [
  {
    name: "Basic Profile Access",
    description:
      "Access to your basic profile information including username and profile picture",
    required: true,
  },
  {
    name: "Media Access",
    description: "Read access to your Instagram posts, photos, and videos",
    required: true,
  },
  {
    name: "Media Insights",
    description:
      "Access to basic engagement metrics like likes and comments count",
    required: true,
  },
];

export function InstagramBasicConnector({
  connectionId,
  token,
}: InstagramBasicConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // Redirect to Meta OAuth authorize endpoint
      const authUrl = new URL(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/meta/authorize`
      );

      if (connectionId) {
        authUrl.searchParams.set("connection_id", connectionId);
      }
      if (token) {
        authUrl.searchParams.set("token", token);
      }
      if (!connectionId && !token) {
        throw new Error("No connection ID or token provided");
      }

      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("Instagram Basic OAuth error:", error);
      setIsConnecting(false);
      throw error;
    }
  };

  return (
    <BaseOAuthConnector
      platform="instagram"
      platformName="Instagram (Personal)"
      icon={Instagram}
      description="Connect your personal Instagram account to access your posts and basic insights."
      permissions={permissions}
      onConnect={handleConnect}
      isConnecting={isConnecting}
      connectionId={connectionId}
      token={token}
      color="bg-gradient-to-r from-purple-500 to-pink-500"
    />
  );
}
