"use client";

import { useState } from "react";
import { Building } from "lucide-react";
import { BaseOAuthConnector } from "./base-oauth-connector";

interface InstagramBusinessConnectorProps {
  connectionId: string;
  token?: string;
}

const permissions = [
  {
    name: "Instagram Business Access",
    description: "Access to your Instagram Business or Creator account",
    required: true,
  },
  {
    name: "Content Publishing",
    description:
      "Ability to publish content to your Instagram account (if needed)",
    required: false,
  },
  {
    name: "Page Management",
    description:
      "Access to connected Facebook Pages for Instagram Business accounts",
    required: true,
  },
  {
    name: "Advanced Insights",
    description:
      "Access to detailed analytics, reach, impressions, and audience demographics",
    required: true,
  },
  {
    name: "Story Metrics",
    description: "Access to Instagram Stories insights and performance data",
    required: true,
  },
];

export function InstagramBusinessConnector({
  connectionId,
  token,
}: InstagramBusinessConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // Redirect to Meta OAuth authorize endpoint
      const authUrl = new URL(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/meta/authorize`
      );

      if (connectionId) {
        authUrl.searchParams.set("connection_id", connectionId);
      }
      if (token) {
        authUrl.searchParams.set("token", token);
      }
      if (!connectionId && !token) {
        throw new Error("No connection ID or token provided");
      }

      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("Instagram Business OAuth error:", error);
      setIsConnecting(false);
      throw error;
    }
  };

  return (
    <BaseOAuthConnector
      platform="instagram_business"
      platformName="Instagram Business"
      icon={Building}
      description="Connect your Instagram Business or Creator account for advanced analytics and insights."
      permissions={permissions}
      onConnect={handleConnect}
      isConnecting={isConnecting}
      connectionId={connectionId}
      token={token}
      color="bg-gradient-to-r from-purple-600 to-pink-600"
    />
  );
}
