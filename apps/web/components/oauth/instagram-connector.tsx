"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { BaseOAuthConnector } from "./base-oauth-connector";
import { Instagram } from "lucide-react";

interface InstagramConnectorProps {
  connectionId: string;
  token?: string;
}

export function InstagramConnector({
  connectionId,
  token,
}: InstagramConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const { toast } = useToast();

  const handleConnect = async (
    type: "instagram_business_login" | "instagram_facebook_login"
  ) => {
    setIsConnecting(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8787";
      const oauthUrl = `${apiUrl}/oauth/meta/authorize?connection_id=${connectionId}&type=${type}${
        token ? `&token=${token}` : ""
      }`;
      window.location.href = oauthUrl;
    } catch (error) {
      console.error("Instagram OAuth error:", error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect to Instagram. Please try again.",
        variant: "destructive",
      });
      setIsConnecting(false);
    }
  };

  return (
    <BaseOAuthConnector
      platform="instagram"
      platformName="Instagram"
      icon={Instagram}
      description="Connect your Instagram account to start syncing your posts."
      permissions={[
        {
          name: "Read your profile information",
          description:
            "This allows us to display your profile name and picture.",
          required: true,
        },
        {
          name: "Read your media",
          description:
            "This allows us to sync your posts, including images and videos.",
          required: true,
        },
        {
          name: "Instant Updates",
          description:
            "Receive new posts and comments instantly via the Instagram Graph API.",
          required: false,
        },
      ]}
      isConnecting={isConnecting}
      connectionId={connectionId}
      token={token}
      onConnect={async () => {}}
    >
      <div className="space-y-4 mt-6">
        <button
          onClick={() => handleConnect("instagram_business_login")}
          disabled={isConnecting}
          className="p-4 border rounded-lg text-left hover:bg-muted ring-2 ring-primary w-full"
        >
          <h3 className="font-semibold">Login with Instagram</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Connect directly with your Instagram account.
          </p>
        </button>
        <button
          onClick={() => handleConnect("instagram_facebook_login")}
          disabled={isConnecting}
          className="p-4 border rounded-lg text-left hover:bg-muted w-full"
        >
          <h3 className="font-semibold">Login with Facebook</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Connect via your Facebook Page.
          </p>
        </button>
      </div>
      <div className="text-center mt-4">
        <a
          href="/dashboard/help/upgrade-to-instagram-business"
          target="_blank"
          className="text-sm text-primary hover:underline"
        >
          How to upgrade to a Business Account
        </a>
      </div>
    </BaseOAuthConnector>
  );
}
