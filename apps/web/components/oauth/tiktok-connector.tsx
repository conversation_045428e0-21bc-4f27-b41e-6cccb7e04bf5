"use client";

import { useState } from "react";
import { BaseOAuthConnector } from "./base-oauth-connector";
import { SiT<PERSON><PERSON> } from "@icons-pack/react-simple-icons";

interface TikTokConnectorProps {
  connectionId: string;
  token?: string;
}

const permissions = [
  {
    name: "User Info",
    description: "Access to basic TikTok account information",
    required: true,
  },
  {
    name: "Video Data",
    description: "Read access to videos you've posted on TikTok",
    required: true,
  },
  {
    name: "Video Insights",
    description: "Access to video analytics, views, and engagement data",
    required: true,
  },
  {
    name: "User Engagement",
    description: "Access to likes, comments, and shares on your TikTok content",
    required: true,
  },
];

export function TikTokConnector({ connectionId, token }: TikTokConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // Redirect to TikTok OAuth authorize endpoint
      const authUrl = new URL(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/tiktok/authorize`
      );

      if (connectionId) {
        authUrl.searchParams.set("connection_id", connectionId);
      }
      if (token) {
        authUrl.searchParams.set("token", token);
      }
      if (!connectionId && !token) {
        throw new Error("No connection ID or token provided");
      }

      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("Error initiating TikTok OAuth flow:", error);
      setIsConnecting(false);
    }
  };

  return (
    <BaseOAuthConnector
      platform="tiktok"
      platformName="TikTok"
      icon={SiTiktok}
      description="Connect your TikTok account to access your videos and engagement data."
      permissions={permissions}
      isConnecting={isConnecting}
      onConnect={handleConnect}
      connectionId={connectionId}
      token={token}
      color="bg-black"
    />
  );
}
