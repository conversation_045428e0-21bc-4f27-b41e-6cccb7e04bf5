"use client";

import { useState } from "react";
import { Youtube } from "lucide-react";
import { BaseOAuthConnector } from "./base-oauth-connector";

interface YouTubeConnectorProps {
  connectionId: string;
  token?: string;
}

const permissions = [
  {
    name: "Channel Access",
    description: "Read-only access to your YouTube channel information",
    required: true,
  },
  {
    name: "Video Data",
    description:
      "Access to your video metadata, titles, descriptions, and thumbnails",
    required: true,
  },
  {
    name: "Analytics Data",
    description:
      "Access to video performance metrics like views, likes, and comments count",
    required: true,
  },
  {
    name: "Subscriber Information",
    description: "Access to your channel's subscriber count and growth metrics",
    required: true,
  },
  {
    name: "Channel Memberships",
    description: "Access to channel membership data (if applicable)",
    required: false,
  },
];

export function YouTubeConnector({
  connectionId,
  token,
}: YouTubeConnectorProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);

    try {
      // Redirect to YouTube OAuth authorize endpoint
      const authUrl = new URL(
        `${process.env.NEXT_PUBLIC_API_URL}/oauth/youtube/authorize`
      );

      if (connectionId) {
        authUrl.searchParams.set("connection_id", connectionId);
      }
      if (token) {
        authUrl.searchParams.set("token", token);
      }
      if (!connectionId && !token) {
        throw new Error("No connection ID or token provided");
      }

      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("YouTube OAuth error:", error);
      setIsConnecting(false);
      throw error;
    }
  };

  return (
    <BaseOAuthConnector
      platform="youtube"
      platformName="YouTube"
      icon={Youtube}
      description="Connect your YouTube channel to access video analytics and subscriber data."
      permissions={permissions}
      onConnect={handleConnect}
      isConnecting={isConnecting}
      connectionId={connectionId}
      token={token}
      color="bg-red-600"
    />
  );
}
