import React from "react";
import { Badge } from "../ui/badge";

const StatusBadge: React.FC<{ status: string; className?: string }> = ({
  status,
  className,
}) => {
  const variants = {
    active: "default",
    connected: "default",
    paused: "secondary",
    disabled: "secondary",
    error: "destructive",
  } as const;

  return (
    <Badge
      variant={variants[status as keyof typeof variants] || "outline"}
      className={className}
    >
      {status}
    </Badge>
  );
};

export default StatusBadge;
