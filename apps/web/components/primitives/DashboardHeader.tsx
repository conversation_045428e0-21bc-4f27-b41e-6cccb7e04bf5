import React from "react";
//import { SidebarTrigger } from "../ui/sidebar";
import { Separator } from "../ui/separator";
import Link from "next/link";
import { But<PERSON> } from "../ui/button";
import { ArrowLeft } from "lucide-react";
import { Badge } from "../ui/badge";

const DashboardHeader: React.FC<{
  title: string;
  id?: string;
  link?: { label: string; href: string };
  icon?: React.ReactNode;
  children?: React.ReactNode;
}> = ({ title, link, icon, children, id }) => {
  return (
    <header className="flex h-14 shrink-0 items-center gap-2 transition-[width,height] ease-linear">
      <div className="flex items-center gap-2">
        {/*}<SidebarTrigger className="-ml-1" />{*/}
        {link && (
          <Link href={link.href}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {link.label}
            </Button>
          </Link>
        )}
        {link && <Separator orientation="vertical" className="mr-2 h-4" />}
        {icon}
        <h1 className="h1">{title}</h1>
        {id && <Badge variant="outline">{id}</Badge>}
      </div>
      {children}
    </header>
  );
};

export default DashboardHeader;
