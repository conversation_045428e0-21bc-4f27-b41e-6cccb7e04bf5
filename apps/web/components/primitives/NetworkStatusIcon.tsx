import { CheckCircle, XCircle } from "lucide-react";
import React from "react";

const NetworkStatusIcon: React.FC<{ status: string }> = ({ status }) => {
  switch (status) {
    case "connected":
      return <CheckCircle className="h-3 w-3 text-green-500" />;
    case "error":
      return <XCircle className="h-3 w-3 text-red-500" />;
    case "disabled":
      return <XCircle className="h-3 w-3 text-gray-400" />;
    default:
      return <XCircle className="h-3 w-3 text-gray-400" />;
  }
};

export default NetworkStatusIcon;
