// src/revalidation-utils.ts

/**
 * Utility to trigger Next.js cache revalidation from the API
 */

interface RevalidationOptions {
  type?: 'connection' | 'project' | 'feed';
  projectId?: string;
  connectionId?: string;
  feedId?: string;
  paths?: string[];
  tags?: string[];
}

export async function triggerRevalidation(
  webUrl: string,
  revalidationSecret: string,
  options: RevalidationOptions
): Promise<boolean> {
  try {
    const revalidationUrl = `${webUrl}/api/revalidate`;
    
    console.log(`Triggering revalidation for:`, options);
    
    const response = await fetch(revalidationUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${revalidationSecret}`,
      },
      body: JSON.stringify(options),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Revalidation failed: ${response.status} ${response.statusText}`, errorText);
      return false;
    }

    const result = await response.json();
    console.log('Revalidation successful:', result);
    return true;

  } catch (error) {
    console.error('Revalidation request failed:', error);
    return false;
  }
}

/**
 * Trigger revalidation for a connection after OAuth completion
 */
export async function revalidateConnection(
  webUrl: string,
  revalidationSecret: string,
  projectId: string,
  connectionId: string
): Promise<boolean> {
  return triggerRevalidation(webUrl, revalidationSecret, {
    type: 'connection',
    projectId,
    connectionId,
  });
}
