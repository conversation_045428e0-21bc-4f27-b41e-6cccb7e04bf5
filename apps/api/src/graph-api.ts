// src/graph-api.ts
import { z } from "zod";
import {
  ApiFetchResult,
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiCommentsResponseSchema,
  BatchApiResponseItemSchema,
  GraphApiComment,
  GraphApiMeSchema,
  GraphApiCommentsResponse,
  GraphApiPagingSchema,
  YouTubeChannelResponseSchema,
  YouTubeVideosResponseSchema,
} from "./types";
import { logErrorToAnalytics } from "./analytics-utils"; // Import error logger

import * as constants from "./constants";

// --- Fehlerklasse ---
export class GraphApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public isRetryable: boolean = true,
    public isAuthError: boolean = false
  ) {
    super(message);
    this.name = "GraphApiError";
  }
}

// --- Retry Helper ---
async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  contextInfo: string,
  env: Bindings
): Promise<Response> {
  let attempts = 0;
  let delay = constants.INITIAL_RETRY_DELAY_MS;

  while (attempts < constants.MAX_API_RETRIES) {
    attempts++;
    try {
      const response = await fetch(url, options);
      if (response.ok) return response;

      const status = response.status;
      // Versuche Body zu lesen für Fehlerdetails
      let errorBodyText: string | null = null;
      try {
        errorBodyText = await response.clone().text();
      } catch {
        /* ignore */
      }

      console.error(
        `GRAPH_API: Error (Attempt ${attempts}, Status ${status}) for ${contextInfo}:`,
        errorBodyText ?? "(no body)"
      );

      if (status >= 400 && status < 500 && status !== 429) {
        const isAuth =
          [401, 403].includes(status) ||
          (status === 400 && errorBodyText?.includes("OAuthException"));
        throw new GraphApiError(
          `Non-retryable API error ${status}`,
          status,
          false,
          isAuth
        );
      }
      if (attempts >= constants.MAX_API_RETRIES) {
        throw new GraphApiError(
          `API call failed after ${attempts} attempts (Status ${status})`,
          status,
          false
        );
      }
    } catch (error: any) {
      console.error(
        `GRAPH_API: Network/Fetch Error (Attempt ${attempts}) for ${contextInfo}:`,
        error
      );
      if (error instanceof GraphApiError) throw error;
      if (attempts >= constants.MAX_API_RETRIES) {
        throw new GraphApiError(
          `API call failed after ${attempts} attempts (Network/Fetch Error)`,
          undefined,
          false
        );
      }
    }
    console.log(
      `GRAPH_API: Waiting ${delay}ms before next retry for ${contextInfo}`
    );
    await new Promise((resolve) => setTimeout(resolve, delay));
    delay *= 2;
  }
  throw new GraphApiError(
    "API call failed unexpectedly after max retries",
    undefined,
    false
  );
}

// --- Graph API Funktionen ---

export async function fetchBatchMediaDataAndFirstComments(
  mediaIds: string[],
  accessToken: string,
  env: Bindings
): Promise<
  | {
      mediaId: string;
      success: boolean;
      data?: ApiFetchResult;
      error?: any;
      isAuthError?: boolean;
    }[]
  | null
> {
  if (mediaIds.length === 0) return [];
  const contextInfo = `Batch Fetch (${mediaIds.length} items)`;
  const baseFields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const batchPayload: { method: string; relative_url: string }[] = [];
  for (const id of mediaIds) {
    batchPayload.push({
      method: "GET",
      relative_url: `${constants.META_API_VERSION}/${id}?fields=${baseFields}`,
    });
    // Skip adding comment requests to batch payload
  }

  // Simplified implementation without retry logic since we're not fetching comments
  try {
    const url = `https://graph.facebook.com/${constants.META_API_VERSION}/`;
    const body = new URLSearchParams();
    body.append("access_token", accessToken);
    body.append("batch", JSON.stringify(batchPayload));
    const response = await fetch(url, { method: "POST", body: body });

    if (!response.ok) {
      throw new GraphApiError(
        `Batch API request failed with status ${response.status}`,
        response.status,
        false
      );
    }

    const resultsJson = await response.json();
    const resultsValidation = z
      .array(BatchApiResponseItemSchema)
      .safeParse(resultsJson);
    if (
      !resultsValidation.success ||
      resultsValidation.data.length !== batchPayload.length
    ) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Batch API response structure or length mismatch",
        {
          context: contextInfo,
          errors: resultsValidation.error?.flatten(),
          receivedCount: Array.isArray(resultsJson)
            ? resultsJson.length
            : "N/A",
          expectedCount: batchPayload.length,
        }
      );
      throw new GraphApiError(
        "Unexpected Batch API response format",
        undefined,
        false
      );
    }
    const results = resultsValidation.data;

    // Process batch results with Zod validation for each body
    const processedResults: {
      mediaId: string;
      success: boolean;
      data?: ApiFetchResult;
      error?: any;
      isAuthError?: boolean;
    }[] = [];
    const resultMap: {
      [key: string]: {
        baseData?: GraphApiMedia;
        baseError?: any;
        isAuthError?: boolean;
      };
    } = {};

    results.forEach((res, index) => {
      const originalRequest = batchPayload[index];
      const urlMatch =
        originalRequest.relative_url.match(/v\d+\.\d+\/([^/?]+)/);
      if (!urlMatch || !urlMatch[1]) return;
      const mediaId = urlMatch[1];
      if (!resultMap[mediaId]) resultMap[mediaId] = {};
      const code = res.code;
      let bodyData = null;
      let parseError = null;
      try {
        bodyData = JSON.parse(res.body);
      } catch (e) {
        parseError = e;
      }

      if (code >= 200 && code < 300 && bodyData) {
        const mediaValidation = GraphApiMediaSchema.safeParse(bodyData);
        if (mediaValidation.success)
          resultMap[mediaId].baseData = mediaValidation.data;
        else {
          resultMap[mediaId].baseError = "Invalid media structure";
          logErrorToAnalytics(
            env,
            "API_VALIDATION_ERROR",
            "Invalid media structure in batch",
            { mediaId, errors: mediaValidation.error.flatten() }
          );
        }
      } else {
        const isSubAuthError = [400, 401, 403].includes(code);
        const errorPayload =
          bodyData?.error?.message ||
          parseError ||
          res.body ||
          `Error Code ${code}`;
        console.error(
          `GRAPH_API: Error in Batch sub-request for ${originalRequest.relative_url} (Code: ${code}):`,
          errorPayload
        );
        resultMap[mediaId].baseError = errorPayload;
        if (isSubAuthError) resultMap[mediaId].isAuthError = true;
      }
    });

    // Consolidate results
    for (const mediaId of mediaIds) {
      const result = resultMap[mediaId];
      if (result?.baseData) {
        processedResults.push({
          mediaId,
          success: true,
          data: {
            baseData: result.baseData,
            fetchedComments: [], // No comments fetched
            nextPageUrl: null, // No next page URL for comments
          },
        });
      } else {
        processedResults.push({
          mediaId,
          success: false,
          error: result?.baseError || "Unknown error",
          isAuthError: result?.isAuthError,
        });
      }
    }

    return processedResults;
  } catch (error: any) {
    console.error(`GRAPH_API: Batch fetch failed:`, error);
    logErrorToAnalytics(env, "BATCH_FETCH_ERROR", "Batch fetch failed", {
      error: String(error),
    });
    // Return error results for all media IDs
    return mediaIds.map((mediaId) => ({
      mediaId,
      success: false,
      error:
        error instanceof GraphApiError
          ? error
          : new GraphApiError(String(error)),
      isAuthError: error instanceof GraphApiError && error.isAuthError,
    }));
  }
}

// --- Basic Display API Funktionen ---

export async function fetchBasicDisplayMedia() {
  // This function is deprecated and should not be used.
  return null;
}

export async function checkBasicDisplayToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://graph.instagram.com/${constants.BASIC_DISPLAY_API_VERSION}/me?fields=id&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;
    const jsonResp = await response.json();
    const validation = GraphApiMeSchema.safeParse(jsonResp);
    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Basic Display /me response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return false;
    }
    return true;
  } catch (e) {
    console.error("Basic Display token check failed:", e);
    return false;
  }
}

// --- YouTube API Functions ---

/**
 * Fetches channel information including subscriber count
 */
export async function fetchYouTubeChannelInfo(
  channelId: string,
  accessToken: string,
  env: Bindings
): Promise<{ channel: any; error?: any } | null> {
  const contextInfo = `YouTube Channel ${channelId}`;
  try {
    const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id=${channelId}&access_token=${accessToken}`;

    const response = await fetchWithRetry(url, {}, `${contextInfo} Info`, env);

    const jsonResp = await response.json();
    const validation = YouTubeChannelResponseSchema.safeParse(jsonResp);

    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "YOUTUBE_CHANNEL_VALIDATION_ERROR",
        "Invalid YouTube channel response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return null;
    }

    if (validation.data.items.length === 0) {
      console.warn(`YouTube: No channel found for ID ${channelId}`);
      return null;
    }

    return { channel: validation.data.items[0] };
  } catch (error: any) {
    console.error(
      `YouTube: Failed to fetch channel info for ${channelId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "YOUTUBE_CHANNEL_FETCH_ERROR",
      "Failed fetching YouTube channel",
      { channelId, error: String(error) }
    );
    return { channel: null, error };
  }
}

/**
 * Fetches videos from a YouTube channel
 */
export async function fetchYouTubeChannelVideos(
  channelId: string,
  accessToken: string,
  env: Bindings,
  limit: number = 50
): Promise<{ videos: any[]; error?: any } | null> {
  const contextInfo = `YouTube Videos for ${channelId}`;
  const collectedVideos: any[] = [];
  let pageToken: string | undefined;

  try {
    while (collectedVideos.length < limit) {
      const remainingLimit = limit - collectedVideos.length;
      const currentLimit = Math.min(remainingLimit, 50); // YouTube API max per request

      let url = `https://www.googleapis.com/youtube/v3/search?part=id&channelId=${channelId}&type=video&order=date&maxResults=${currentLimit}&access_token=${accessToken}`;
      if (pageToken) {
        url += `&pageToken=${pageToken}`;
      }

      console.log(`YouTube: Fetching videos page for ${channelId}...`);
      const searchResponse = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Search`,
        env
      );

      const searchJson = (await searchResponse.json()) as any;

      if (!searchJson.items || searchJson.items.length === 0) {
        break;
      }

      // Get video IDs
      const videoIds = searchJson.items
        .map((item: any) => item.id.videoId)
        .join(",");

      // Fetch detailed video information
      const detailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${videoIds}&access_token=${accessToken}`;
      const detailsResponse = await fetchWithRetry(
        detailsUrl,
        {},
        `${contextInfo} Details`,
        env
      );

      const detailsJson = await detailsResponse.json();
      const validation = YouTubeVideosResponseSchema.safeParse(detailsJson);

      if (!validation.success) {
        logErrorToAnalytics(
          env,
          "YOUTUBE_VIDEOS_VALIDATION_ERROR",
          "Invalid YouTube videos response",
          { errors: validation.error.flatten(), received: detailsJson }
        );
        break;
      }

      collectedVideos.push(...validation.data.items);

      pageToken = searchJson.nextPageToken;
      if (!pageToken || collectedVideos.length >= limit) {
        break;
      }
    }

    console.log(
      `YouTube: Fetched ${collectedVideos.length} videos for ${channelId}.`
    );
    return { videos: collectedVideos.slice(0, limit) };
  } catch (error: any) {
    console.error(`YouTube: Failed to fetch videos for ${channelId}:`, error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_VIDEOS_FETCH_ERROR",
      "Failed fetching YouTube videos",
      { channelId, error: String(error) }
    );
    return { videos: [], error };
  }
}

/**
 * Checks if a YouTube access token is valid
 */
export async function checkYouTubeToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://www.googleapis.com/youtube/v3/channels?part=id&mine=true&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;

    const jsonResp = (await response.json()) as any;
    return jsonResp.items && jsonResp.items.length > 0;
  } catch (e) {
    console.error("YouTube token check failed:", e);
    return false;
  }
}
