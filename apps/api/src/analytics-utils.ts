// src/analytics-utils.ts
import { Bindings } from "./types";

/**
 * Logs an error event to the APP_ERRORS Analytics Engine dataset.
 * Uses waitUntil for fire-and-forget logging.
 * @param env Worker bindings including APP_ERRORS dataset.
 * @param type A category for the error (e.g., 'DO_ALARM_ERROR'). Used for indexing/filtering.
 * @param message A human-readable error message.
 * @param data Additional context object, will be JSON stringified. Include relevant IDs.
 */
export function logErrorToAnalytics(
  env: Bindings,
  type: string,
  message?: string,
  data: object = {}
) {
  if (!env.APP_ERRORS) {
    console.error(
      `[NO ANALYTICS] Error Type: ${type}, Message: ${message}, Data:`,
      data
    );
    return;
  }
  try {
    // Construct data point structure
    const dataPoint: AnalyticsEngineDataPoint = {
      blobs: [
        type || "UNKNOWN_TYPE",
        message || "Unknown error message",
        JSON.stringify(data),
        // Optional: Add component name based on where it's called
        // data?.componentName || "UNKNOWN_COMPONENT"
      ],
      // indexes must have exactly the same number of elements as defined in wrangler.toml or dashboard (if indexed)
      // Example assuming first blob 'type' is indexed:
      // indexes: [type]
      // Example assuming type and platformConnectionId (if present) are indexed:
      indexes: [
        type || "UNKNOWN_TYPE",
        (data as any)?.platformConnectionId ||
          (data as any)?.feedId ||
          "ID_UNKNOWN", // Example index
      ],
    };

    // Using Promise.resolve().then() ensures waitUntil gets a promise
    const promise = Promise.resolve()
      .then(() => {
        env.APP_ERRORS.writeDataPoint(dataPoint);
      })
      .catch((e) => {
        console.error("Failed to write error to Analytics Engine:", type, e);
      });

    // Try to find execution context to use waitUntil
    if (
      typeof WorkerGlobalScope !== "undefined" &&
      self instanceof WorkerGlobalScope
    ) {
      // Standard Worker context
      (self as any).waitUntil(promise);
    } else {
      // Fallback for environments where context might be tricky (e.g. testing)
      // Or pass ctx explicitly if available
      promise; // Fire and forget without explicit waitUntil
    }
  } catch (e) {
    console.error(
      "Error constructing promise for Analytics Engine logging:",
      e
    );
  }
}
