import { DateTime } from "luxon";

export const getConnectionStatus = ({
  isActive,
  hasError,
  tokenExpiresAt,
  isConnected,
}: {
  isActive: boolean;
  hasError: boolean;
  tokenExpiresAt: Date | null;
  isConnected: boolean;
}) => {
  switch (true) {
    case !isActive:
      return "inactive" as const;
    case hasError:
      return "error" as const;
    case tokenExpiresAt && DateTime.fromJSDate(tokenExpiresAt) < DateTime.now():
      return "expired" as const;
    case isConnected:
      return "active" as const;
    default:
      return "auth_needed" as const;
  }
};
