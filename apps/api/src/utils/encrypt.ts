import CryptoJS from "crypto-js";

export async function encrypt(data: string, secret: string): Promise<string> {
  return CryptoJS.AES.encrypt(data, secret).toString();
}

export async function decrypt(
  encryptedData: string,
  secret: string
): Promise<string> {
  console.log("Decrypting data with secret:", secret);
  const bytes = CryptoJS.AES.decrypt(encryptedData, secret);
  if (!bytes) {
    throw new Error("Decryption failed, invalid data or secret");
  }
  return bytes.toString(CryptoJS.enc.Utf8);
}
