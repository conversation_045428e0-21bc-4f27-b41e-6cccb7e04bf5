import { createMiddleware } from "hono/factory";
import { AppContext } from "../../types";
import { HTTPException } from "hono/http-exception";

export const userAuthRequired = createMiddleware<AppContext>(
  async (c, next) => {
    const user = c.get("user");
    const org = c.get("org");

    if (!user || !org) {
      console.warn(
        "authRequired check failed: Missing user or organization context."
      );
      throw new HTTPException(403, {
        message:
          "A valid user session within an organization context is required.",
      });
    }

    console.log(
      `authRequired check passed for user ${user.userId} in org ${org.orgId}`
    );
    await next();
  }
);
