import { createMiddleware } from "hono/factory";
import { AppContext } from "../../types";
import { initBaseAuth } from "@propelauth/node";

// ===================================================================================
// NEUE, ZENTRALE PROPEL AUTH MIDDLEWARE (ersetzt Ihre globale 'jose'-Middleware)
// Diese Middleware versucht, einen Benutzer zu authentifizieren, blockiert aber nicht.
// Sie ist dafür verantwortlich, den `user`- und `org`-Kontext zu setzen.
// ===================================================================================
export const propelAuthMiddleware = createMiddleware<AppContext>(
  async (c, next) => {
    const authHeader = c.req.header("Authorization");
    const orgId = c.req.header("X-Org-Id");

    // Nur ausführen, wenn ein Bearer-Token vorhanden ist
    if (authHeader && orgId) {
      try {
        // Initialisiere den Propel-Client mit den Umgebungsvariablen
        const propel = initBaseAuth({
          authUrl: c.env.PROPELAUTH_AUTH_URL,
          apiKey: c.env.PROPELAUTH_API_KEY,
        });

        // Schritt 1: Validiere den Benutzer-Token
        const { user, orgMemberInfo } =
          await propel.validateAccessTokenAndGetUserWithOrgInfo(
            authHeader,
            { orgId: orgId! } // Das '!' sagt TypeScript, dass orgId hier nicht null/undefined sein wird, da wir es oben prüfen
          );

        c.set("user", user); // Benutzer im Kontext speichern
        c.set("org", orgMemberInfo); // Das zurückgegebene Objekt heißt orgMemberInfo
        c.set("organizationId", orgMemberInfo.orgId);

        console.log(
          `Propel Auth context set for user ${user.userId} in org ${orgMemberInfo.orgName}`
        );
      } catch (error) {
        // Wenn die Validierung fehlschlägt, loggen wir das gesamte Fehlerobjekt.
        console.error("--- DETAILLIERTER PROPEL AUTH VALIDATION ERROR ---");
        console.error(error); // Gibt das komplette Fehlerobjekt mit allen Details aus
        console.error("--- ENDE DETAILLIERTER ERROR ---");
      }
    }

    await next();
  }
);
