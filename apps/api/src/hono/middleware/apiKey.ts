import { createMiddleware } from "hono/factory";
import { AppContext } from "../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient, validateApiKey } from "../../database-service";
import { ContentfulStatusCode } from "hono/utils/http-status";
import { logErrorToAnalytics } from "../../analytics-utils";

export const apiKeyAuthMiddleware = createMiddleware<AppContext>(
  async (c, next) => {
    console.log("API Key Auth Middleware running...");
    const authHeader = c.req.header("Authorization");
    const bearerPrefix = "Bearer ";
    if (!authHeader || !authHeader.startsWith(bearerPrefix))
      throw new HTTPException(401, {
        message: "Missing or invalid Authorization header",
      });
    const apiKey = authHeader.substring(bearerPrefix.length);
    if (!apiKey) throw new HTTPException(401, { message: "API key missing" });
    try {
      const db = getDbClient(c.env.DB);
      const validationResult = await validateApiKey(db, apiKey);
      if (!validationResult.isValid || !validationResult.feedId) {
        throw new HTTPException(
          (validationResult.status as ContentfulStatusCode) ?? 401,
          {
            message: validationResult.error,
          }
        );
      }
      // Authorization check: Does the feed ID in the path match the one the key is authorized for?
      const requestedFeedId = c.req.param("feedId");
      if (requestedFeedId && validationResult.feedId !== requestedFeedId) {
        console.warn(
          `API Key valid for feed ${validationResult.feedId} but requested feed ${requestedFeedId}`
        );
        logErrorToAnalytics(
          c.env,
          "AUTH_APIKEY_FEED_MISMATCH",
          "API Key not valid for requested feed",
          {
            keyPrefix: apiKey.substring(0, 3),
            requestedFeedId,
            authorizedFeedId: validationResult.feedId,
          }
        );
        throw new HTTPException(403, {
          message: "API key not valid for this feed",
        });
      }
      c.set("authorizedFeedId", validationResult.feedId);
      c.set("apiKey", apiKey);

      // Set organization context from API key validation
      if (validationResult.organizationId) {
        c.set("organizationId", validationResult.organizationId);
      }

      console.log(
        `API Key validated for feed: ${validationResult.feedId}, org: ${validationResult.organizationId || "unknown"}`
      );
    } catch (e) {
      if (e instanceof HTTPException) throw e;
      console.error("API key validation error:", e);
      logErrorToAnalytics(
        c.env,
        "AUTH_APIKEY_ERROR",
        "API key validation failed",
        { error: String(e) }
      );
      throw new HTTPException(500, {
        message: "Internal server error during authentication",
      });
    }
    await next();
  }
);
