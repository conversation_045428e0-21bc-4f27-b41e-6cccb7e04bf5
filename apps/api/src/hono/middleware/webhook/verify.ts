import { createMiddleware } from "hono/factory";
import { AppContext, Platform } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { logErrorToAnalytics } from "../../../analytics-utils";

// Helper function to convert hex strings to ArrayBuffer
function hexToBytes(hex: string): ArrayBuffer {
  const buffer = new ArrayBuffer(hex.length / 2);
  const dataView = new DataView(buffer);
  for (let i = 0; i < hex.length; i += 2) {
    dataView.setUint8(i / 2, parseInt(hex.substring(i, i + 2), 16));
  }
  return buffer;
}

// Configuration interface for each platform's verification logic
interface VerificationConfig {
  platform: Platform;
  verifyGet?: (c: any) => Response | Promise<Response>;
  verifyPost?: (c: any) => Promise<void>;
}

// Platform-specific verification logic
const metaVerification: VerificationConfig = {
  platform: "instagram", // Applies to both Instagram and Facebook
  verifyGet: (c) => {
    const mode = c.req.query("hub.mode");
    const token = c.req.query("hub.verify_token");
    const challenge = c.req.query("hub.challenge");
    const verifyToken = c.env.YOUR_VERIFY_TOKEN;

    if (!verifyToken) {
      console.error("YOUR_VERIFY_TOKEN not set!");
      return c.text("Config Error", 500);
    }
    if (mode === "subscribe" && token === verifyToken && challenge) {
      console.log("Meta webhook verification successful!");
      return c.text(challenge);
    } else {
      console.error("Meta webhook verification failed.");
      throw new HTTPException(403, { message: "Forbidden" });
    }
  },
  verifyPost: async (c) => {
    const signatureHeader = c.req.header("X-Hub-Signature-256");
    if (!signatureHeader) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_AUTH_FAIL",
        "Missing X-Hub-Signature-256"
      );
      throw new HTTPException(401, {
        message: "Unauthorized: Missing signature",
      });
    }

    const rawBody = await c.req.raw.clone().text();
    const secret = c.env.INSTAGRAM_APP_SECRET;
    if (!secret) throw new Error("INSTAGRAM_APP_SECRET not configured");

    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      "raw",
      encoder.encode(secret),
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["verify"]
    );
    const signatureBuffer = hexToBytes(signatureHeader.replace("sha256=", ""));
    const isValid = await crypto.subtle.verify(
      "HMAC",
      key,
      signatureBuffer,
      encoder.encode(rawBody)
    );

    if (!isValid) {
      logErrorToAnalytics(c.env, "WEBHOOK_AUTH_FAIL", "Invalid signature");
      throw new HTTPException(401, {
        message: "Unauthorized: Invalid signature",
      });
    }
  },
};

const youtubeVerification: VerificationConfig = {
  platform: "youtube",
  verifyGet: (c) => {
    const mode = c.req.query("hub.mode");
    const challenge = c.req.query("hub.challenge");
    const verifyToken = c.req.query("hub.verify_token");
    const expectedToken = c.env.YOUTUBE_VERIFY_TOKEN || "youtube_verify_token";

    if (
      (mode === "subscribe" || mode === "unsubscribe") &&
      verifyToken === expectedToken &&
      challenge
    ) {
      return c.text(challenge);
    }
    throw new HTTPException(403, { message: "Verification failed" });
  },
};

const tiktokVerification: VerificationConfig = {
  platform: "tiktok",
  verifyGet: (c) => {
    const challenge =
      c.req.query("challenge") ||
      c.req.query("hub.challenge") ||
      c.req.query("echo");
    if (challenge) {
      return c.text(challenge);
    }
    return c.json({ status: "ok" });
  },
};

const configs: Record<string, VerificationConfig> = {
  meta: metaVerification,
  youtube: youtubeVerification,
  tiktok: tiktokVerification,
};

/**
 * Creates a generic webhook verification middleware.
 * The actual verification logic is determined by the `platform` parameter.
 */
export const createWebhookVerificationMiddleware = (platform: string) => {
  return createMiddleware<AppContext>(async (c, next) => {
    const config = configs[platform];
    if (!config) {
      throw new Error(`Invalid platform: ${platform}`);
    }

    try {
      if (c.req.method === "GET" && config.verifyGet) {
        const response = await config.verifyGet(c);
        if (response) return response;
      }
      if (c.req.method === "POST" && config.verifyPost) {
        await config.verifyPost(c);
      }
    } catch (error) {
      if (error instanceof HTTPException) throw error;
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_AUTH_ERROR",
        `Error during ${platform} signature verification`,
        { error: String(error) }
      );
      throw new HTTPException(500, { message: "Verification Error" });
    }

    await next();
  });
};
