import { User } from "@propelauth/node";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";

export const fixUserAndOrgTable = async ({
  user,
  d1,
}: {
  user: User;
  d1: D1Database;
}) => {
  const db = getDbClient(d1);
  const org = user.activeOrgId
    ? user.orgIdToOrgMemberInfo?.[user.activeOrgId]
    : null;

  try {
    await db
      .insert(schema.users)
      .values({
        id: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .onConflictDoNothing()
      .run();

    if (org) {
      await db
        .insert(schema.organizations)
        .values({
          id: org.orgId,
          name: org.orgName,
        })
        .onConflictDoNothing()
        .run();

      await db
        .insert(schema.organizationMembers)
        .values({
          organizationId: org.orgId,
          userId: user.userId,
          role: org.assignedRole as "admin" | "member" | "owner",
        })
        .onConflictDoNothing()
        .run();
    }
  } catch (e) {
    console.error(`Failed to fix user ${user.userId} in DB:`, e);
  }
};
