import { Context } from "hono";
import { AppContext } from "../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { getConnectionStatus } from "../../../utils/connectionStatus";

export const getAllProjects = async (
  c: Context<AppContext, "/manage/projects", BlankInput>
) => {
  console.log("ALL PROJECTS");
  const page = Math.min(
    Math.max(1, parseInt(c.req.query("page") || "1", 10)),
    1000
  );

  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  );

  console.log("PAGEE; LIMIT", page, limit);

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);
  try {
    const projects = await db.query.projects.findMany({
      columns: {
        id: true,
        name: true,
        description: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        feeds: {
          columns: {
            id: true,
            name: true,
            description: true,
            isActive: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        platformConnections: {
          columns: {
            id: true,
            lastPolledAt: true,
            isActive: true,
            hasError: true,
            isConnected: true,
            tokenExpiresAt: true,
          },
        },
      },
      where: (projectsTable, { eq, and }) => {
        return and(eq(projectsTable.organizationId, organizationId));
      },
      limit: limit,
      offset: page ? limit * (page - 1) : 0,
    });

    return c.json(
      projects.map((project) => ({
        ...project,
        platformConnections: project.platformConnections.map((conn) => ({
          ...conn,
          status: getConnectionStatus({
            isActive: conn.isActive,
            hasError: conn.hasError,
            tokenExpiresAt: conn.tokenExpiresAt,
            isConnected: conn.isConnected,
          }),
        })),
      }))
    );
  } catch (e) {
    console.error(`Failed to fetch projects for org ${organizationId}:`, e);
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FETCH_ERROR",
      `Failed to fetch projects`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch projects",
    });
  }
};
