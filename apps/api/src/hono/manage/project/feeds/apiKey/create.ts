import { Context } from "hono";
import { AppContext } from "../../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../../manage/account/fix";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";
import { apiKey } from "@socialfeed/utils/nanoid";
import { hashApiKey } from "../../../../../database-service";

const validator = z.object({
  name: z.string(),
  expiresAt: z.date().optional().nullable(),
});

export const createApiKeyValidator = zValidator("json", validator);

export const createApiKey = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/feeds/:feedId/api-keys",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;
  const expiresAt = validated.expiresAt;
  const userId = c.var.user?.userId;

  if (!name || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const projectId = c.req.param("projectId");
  const feedId = c.req.param("feedId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .innerJoin(schema.feeds, eq(schema.feeds.projectId, schema.projects.id))
      .where(
        and(
          eq(schema.feeds.id, feedId),
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, { message: "Project not found" });
    }

    const key = apiKey();
    const keyPreview = `${key.slice(0, 4)}************************${key.slice(-4)}`;

    await db.insert(schema.apiKeys).values({
      key: hashApiKey(key),
      keyPreview: keyPreview,
      feedId: feedId,
      createdBy: userId,
      description: name,
      expiresAt: expiresAt ?? null,
    });

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to add apiKey to feed ${feedId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "KEY_ADD_ERROR", `Failed to add key`, {
      feedId,
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });

    throw new HTTPException(500, {
      message: "Failed to add key",
    });
  }
};
