import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { DateTime } from "luxon";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

const validator = z.object({
  name: z.string(),
  description: z.string().optional().nullable(),
  connections: z.array(z.string()),
});

export const createFeedValidator = zValidator("json", validator);

export const createFeed = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;
  const description = validated.description;
  const connections = validated.connections;
  const userId = c.var.user?.userId;

  if (!name || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const projectId = c.req.param("projectId");

  const db = getDbClient(c.env.DB);
  let feedId: string | null = null;
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, { message: "Project not found" });
    }

    const feed = await db
      .insert(schema.feeds)
      .values({
        projectId: projectId,
        name: name,
        description: description,
        createdBy: userId,
        createdAt: DateTime.now().toJSDate(),
        updatedAt: DateTime.now().toJSDate(),
      })
      .returning({ id: schema.feeds.id })
      .get();
    feedId = feed.id;
    connections.forEach(async (connection) => {
      await db.insert(schema.feedConnections).values({
        feedId: feed.id,
        platformConnectionId: connection,
      });
    });

    return c.json({
      id: feedId,
    });
  } catch (e) {
    console.error(
      `Failed to add feed to project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "FEED_ADD_ERROR", `Failed to add feed`, {
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });

    if (feedId) {
      connections.forEach(async (connection) => {
        try {
          await db
            .delete(schema.feedConnections)
            .where(
              and(
                eq(schema.feedConnections.feedId, feedId!),
                eq(schema.feedConnections.platformConnectionId, connection)
              )
            );
        } catch (e) {
          console.error(
            `Failed to clean up feed connection ${connection} for feed ${feedId}:`,
            e
          );
        }
      });
      try {
        await db.delete(schema.feeds).where(eq(schema.feeds.id, feedId!));
      } catch (e) {
        console.error(`Failed to clean up feed ${feedId}:`, e);
      }
    }

    throw new HTTPException(500, {
      message: "Failed to add feed",
    });
  }
};
