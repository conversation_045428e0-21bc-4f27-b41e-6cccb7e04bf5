import { Context } from "hono";
import { AppContext } from "../../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";
import { getConnectionStatus } from "../../../../utils/connectionStatus";

export const getAllFeeds = async (
  c: Context<AppContext, "/manage/project/:projectId/feeds", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const projectId = c.req.param("projectId");

  const db = getDbClient(c.env.DB);
  try {
    const validation = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .get();

    if (!validation) {
      console.error(validation);
      throw new HTTPException(404, {
        message: "Project not found",
      });
    }

    const data = await db.query.feeds.findMany({
      with: {
        connections: {
          with: {
            platformConnection: true,
          },
        },
        apiKeys: true,
        project: {
          columns: {
            organizationId: true,
          },
        },
      },
      where: (feedsTable, { eq, and }) => {
        return and(eq(feedsTable.projectId, projectId));
      },
    });

    if (!data)
      throw new HTTPException(404, {
        message: "Project not found",
      });

    return c.json(
      data.map((feed) => ({
        ...feed,
        connections: feed.connections
          .map((conn) => conn.platformConnection)
          .map((conn) => ({
            id: conn.id,
            name: conn.name,
            platform: conn.platform,
            createdAt: conn.createdAt,
            isActive: conn.isActive,
            hasError: conn.hasError,
            isConnected: conn.isConnected,
            tokenExpiresAt: conn.tokenExpiresAt,
            lastPolledAt: conn.lastPolledAt,
            platformAccountName: conn.platformAccountName,
            lastCheckedAt: conn.lastCheckedAt,
            status: getConnectionStatus({
              isActive: conn.isActive,
              hasError: conn.hasError,
              tokenExpiresAt: conn.tokenExpiresAt,
              isConnected: conn.isConnected,
            }),
          })),
      }))
    );
  } catch (e) {
    console.error(
      `Failed to fetch feeds for project ${projectId} in org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FEEDS_FETCH_ERROR",
      `Failed to fetch project feeds`,
      {
        organizationId,
        projectId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch project feeds",
    });
  }
};
