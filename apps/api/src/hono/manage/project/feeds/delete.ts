import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { and, eq } from "drizzle-orm";
import { BlankInput } from "hono/types";

export const deleteFeed = async (
  c: Context<AppContext, "/manage/projects/:projectId/feed/:feedId", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "Authentification failed" });

  const projectId = c.req.param("projectId");
  const feedId = c.req.param("feedId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.feeds.id,
      })
      .from(schema.feeds)
      .innerJoin(
        schema.projects,
        eq(schema.feeds.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.feeds.id, feedId),
          eq(schema.feeds.projectId, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, {
        message: "Project or Feed or Key not found",
      });
    }

    await db.delete(schema.feeds).where(eq(schema.feeds.id, feedId)).run();

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to delete feed ${feedId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "FEED_DELETE_ERROR", `Failed to delete feed`, {
      feedId,
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to delete feed",
    });
  }
};
