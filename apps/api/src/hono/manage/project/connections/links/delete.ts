import { Context } from "hono";
import { AppContext } from "../../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../../manage/account/fix";
import { and, eq } from "drizzle-orm";
import { BlankInput } from "hono/types";

export const deleteLink = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId/links/:linkId",
    BlankInput
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "Authentification failed" });

  const projectId = c.req.param("projectId");
  const connectionId = c.req.param("connectionId");
  const linkId = c.req.param("linkId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
        linkId: schema.generatedLinks.id,
        connectionId: schema.platformConnections.id,
      })
      .from(schema.projects)
      .innerJoin(
        schema.platformConnections,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .innerJoin(
        schema.generatedLinks,
        eq(
          schema.generatedLinks.platformConnectionId,
          schema.platformConnections.id
        )
      )
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId),
          eq(schema.platformConnections.id, connectionId),
          eq(schema.generatedLinks.id, linkId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, {
        message: "Project or Connection not found",
      });
    }

    await db
      .delete(schema.generatedLinks)
      .where(eq(schema.generatedLinks.id, linkId))
      .run();

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to delete link ${linkId} from connection ${connectionId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "LINK_DELETE_ERROR", `Failed to delete Link`, {
      linkId,
      connectionId,
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to delete link",
    });
  }
};
