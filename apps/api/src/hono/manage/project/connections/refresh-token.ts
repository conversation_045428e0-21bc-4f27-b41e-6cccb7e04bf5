import { z<PERSON>alidator } from "@hono/zod-validator";
import { Context } from "hono";
import { z } from "zod";
import { AppContext } from "../../../../types";
import { getDbClient } from "../../../../database-service";
import { eq } from "drizzle-orm";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { HTTPException } from "hono/http-exception";
import { getPlatformAdapter } from "../../../../platforms";

const validator = z.object({
  projectId: z.string(),
  connectionId: z.string(),
});

export const refreshTokenValidator = zValidator("param", validator);

export const refreshToken = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId/refreshtoken",
    {
      in: {
        param: z.infer<typeof validator>;
      };
      out: {
        param: z.infer<typeof validator>;
      };
    }
  >
) => {
  const { connectionId } = c.req.valid("param");
  const db = getDbClient(c.env.DB);

  const connection = await db.query.platformConnections.findFirst({
    where: eq(schema.platformConnections.id, connectionId),
  });

  if (!connection) {
    throw new HTTPException(404, { message: "Connection not found" });
  }

  const platformAdapter = getPlatformAdapter(connection.platform);
  if (!platformAdapter.refreshToken) {
    throw new HTTPException(400, {
      message: "Token refresh not supported for this platform",
    });
  }

  const result = await platformAdapter.refreshToken(connection, c.env);

  if (!result) {
    throw new HTTPException(500, {
      message: "Failed to refresh token",
    });
  }

  return c.json(result);
};
