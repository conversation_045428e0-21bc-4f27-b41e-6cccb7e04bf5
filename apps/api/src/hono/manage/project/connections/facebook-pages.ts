import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";
import { decryptToken } from "../../../../token-utils";
import { getFacebookPages } from "../../../../platforms/facebook";

export const getFacebookPagesForConnection = async (c: Context<AppContext>) => {
  const requestedProjectId = c.req.param("projectId");
  const requestedConnectionId = c.req.param("connectionId");
  const organizationId = c.var.organizationId;

  if (!organizationId) {
    throw new HTTPException(401, {
      message: "Organization ID not found",
    });
  }

  if (!requestedProjectId) {
    throw new HTTPException(400, {
      message: "Project ID is required",
    });
  }

  if (!requestedConnectionId) {
    throw new HTTPException(400, {
      message: "Connection ID is required",
    });
  }

  const db = getDbClient(c.env.DB);
  
  try {
    // Get the connection with project verification
    const connections = await db
      .select()
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    const connection = connections[0].platform_connections;

    // Verify this is a Facebook connection
    if (connection.platform !== "facebook") {
      throw new HTTPException(400, {
        message: "This endpoint is only for Facebook connections",
      });
    }

    if (!connection.accessTokenEncrypted) {
      throw new HTTPException(400, {
        message: "No access token found for this connection",
      });
    }

    // Decrypt the access token
    const accessToken = await decryptToken(
      connection.accessTokenEncrypted,
      c.env.ENCRYPTION_KEY
    );
    if (!accessToken) {
      throw new HTTPException(400, {
        message: "Failed to decrypt access token",
      });
    }

    // Get fresh pages data from Facebook
    const pages = await getFacebookPages(accessToken, c.env);

    return c.json({
      success: true,
      pages: pages,
      currentPageId: connection.platformAccountId,
    });
  } catch (e) {
    console.error(
      `Failed to get Facebook pages for connection ${requestedConnectionId}:`,
      e
    );

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to get Facebook pages",
    });
  }
};
