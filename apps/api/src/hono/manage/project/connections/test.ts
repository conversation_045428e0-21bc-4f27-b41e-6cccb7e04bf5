import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";
import { getPlatformAdapter } from "../../../../platforms";
import { decryptToken } from "../../../../token-utils";

export const testConnection = async (c: Context<AppContext>) => {
  const requestedProjectId = c.req.param("projectId");
  const requestedConnectionId = c.req.param("connectionId");
  const organizationId = c.var.organizationId;

  if (!organizationId) {
    throw new HTTPException(401, {
      message: "Organization ID not found",
    });
  }

  if (!requestedProjectId) {
    throw new HTTPException(400, {
      message: "Project ID is required",
    });
  }

  if (!requestedConnectionId) {
    throw new HTTPException(400, {
      message: "Connection ID is required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    // Get the connection with project verification
    const connections = await db
      .select()
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    const connection = connections[0].platform_connections;

    // Check if connection is active and has a token
    if (!connection.isActive) {
      return c.json({
        success: false,
        error: "Connection is not active",
        status: "inactive",
      });
    }

    if (!connection.isConnected) {
      return c.json({
        success: false,
        error: "Connection is not connected. Please complete the OAuth flow.",
        status: "not_connected",
      });
    }

    if (!connection.accessTokenEncrypted) {
      return c.json({
        success: false,
        error: "No access token found. Please reconnect.",
        status: "no_token",
      });
    }

    // Decrypt the access token
    const accessToken = await decryptToken(
      connection.accessTokenEncrypted,
      c.env.ENCRYPTION_KEY
    );
    if (!accessToken) {
      return c.json({
        success: false,
        error: "Failed to decrypt access token. Please reconnect.",
        status: "token_decrypt_failed",
      });
    }

    // Test the connection using the platform adapter
    try {
      console.log(`Testing connection for platform: ${connection.platform}`);
      const platformAdapter = getPlatformAdapter(connection.platform);
      console.log(
        `Platform adapter found: ${platformAdapter.constructor.name}`
      );

      const isValid = await platformAdapter.checkToken(accessToken, c.env);
      console.log(`Token validation result: ${isValid}`);

      if (isValid) {
        // Update last checked timestamp
        await db
          .update(schema.platformConnections)
          .set({
            lastCheckedAt: new Date(),
            hasError: false,
            needsReconnect: false,
          })
          .where(eq(schema.platformConnections.id, requestedConnectionId));

        return c.json({
          success: true,
          message: `${connection.platform} connection is working properly`,
          status: "connected",
          platform: connection.platform,
          accountName: connection.platformAccountName,
          lastChecked: new Date().toISOString(),
        });
      } else {
        // Mark connection as needing reconnection
        await db
          .update(schema.platformConnections)
          .set({
            lastCheckedAt: new Date(),
            hasError: true,
            needsReconnect: true,
          })
          .where(eq(schema.platformConnections.id, requestedConnectionId));

        return c.json({
          success: false,
          error: `${connection.platform} token is invalid or expired. Please reconnect.`,
          status: "token_invalid",
          platform: connection.platform,
        });
      }
    } catch (adapterError) {
      console.error(
        `Platform adapter error for ${connection.platform}:`,
        adapterError
      );

      // Log the error for analytics
      logErrorToAnalytics(
        c.env,
        "CONNECTION_TEST_ERROR",
        `Platform adapter error for ${connection.platform}`,
        {
          connectionId: requestedConnectionId,
          platform: connection.platform,
          error: String(adapterError),
        }
      );

      // Mark connection as having an error
      await db
        .update(schema.platformConnections)
        .set({
          lastCheckedAt: new Date(),
          hasError: true,
        })
        .where(eq(schema.platformConnections.id, requestedConnectionId));

      return c.json({
        success: false,
        error: `Failed to test ${connection.platform} connection. Please try again later.`,
        status: "test_failed",
        platform: connection.platform,
      });
    }
  } catch (e) {
    console.error(
      `Failed to test connection ${requestedConnectionId} for project ${requestedProjectId} in org ${organizationId}:`,
      e
    );

    logErrorToAnalytics(
      c.env,
      "CONNECTION_TEST_ERROR",
      "Failed to test connection",
      {
        connectionId: requestedConnectionId,
        projectId: requestedProjectId,
        organizationId,
        error: String(e),
      }
    );

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to test connection",
    });
  }
};
