import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";
import { unsubscribeFromYouTubeChannel } from "../../../../platforms/youtube-pubsub";

export const disconnectConnection = async (c: Context<AppContext>) => {
  const requestedProjectId = c.req.param("projectId");
  const requestedConnectionId = c.req.param("connectionId");
  const organizationId = c.var.organizationId;

  if (!organizationId) {
    throw new HTTPException(401, {
      message: "Organization ID not found",
    });
  }

  if (!requestedProjectId) {
    throw new HTTPException(400, {
      message: "Project ID is required",
    });
  }

  if (!requestedConnectionId) {
    throw new HTTPException(400, {
      message: "Connection ID is required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    // Verify the connection exists and belongs to the organization
    const connections = await db
      .select()
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.platformConnections.id, requestedConnectionId),
          eq(schema.platformConnections.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(1)
      .all();

    if (connections.length < 1) {
      throw new HTTPException(404, {
        message: "Connection not found",
      });
    }

    const connection = connections[0].platform_connections;

    // Unsubscribe from PubSub notifications if it's a YouTube connection
    if (connection.platform === "youtube" && connection.platformAccountId) {
      try {
        await unsubscribeFromYouTubeChannel(
          connection.platformAccountId,
          c.env
        );
        console.log(
          `Unsubscribed from YouTube channel: ${connection.platformAccountId}`
        );
      } catch (unsubscribeError) {
        console.error(
          "Failed to unsubscribe from YouTube channel:",
          unsubscribeError
        );
        // Don't fail the disconnect if unsubscription fails
        logErrorToAnalytics(
          c.env,
          "YOUTUBE_UNSUBSCRIBE_ERROR",
          "Failed to unsubscribe from YouTube channel during disconnect",
          {
            connectionId: requestedConnectionId,
            channelId: connection.platformAccountId,
            error: String(unsubscribeError),
          }
        );
      }
    }

    // Reset the connection by clearing OAuth tokens and setting status
    await db
      .update(schema.platformConnections)
      .set({
        // Clear OAuth tokens
        accessTokenEncrypted: null,
        refreshTokenEncrypted: null,
        tokenExpiresAt: null,

        // Clear platform account info
        platformAccountId: null,
        platformAccountName: null,

        // Update status flags
        isConnected: false,
        hasError: false,
        needsReconnect: false,

        // Update timestamps
        lastCheckedAt: new Date(),
      })
      .where(eq(schema.platformConnections.id, requestedConnectionId));

    // Log the disconnection for analytics
    logErrorToAnalytics(
      c.env,
      "CONNECTION_DISCONNECTED",
      "Connection manually disconnected",
      {
        connectionId: requestedConnectionId,
        projectId: requestedProjectId,
        platform: connection.platform,
        organizationId,
      }
    );

    return c.json({
      success: true,
      message: `${connection.platform} connection has been disconnected successfully`,
      platform: connection.platform,
    });
  } catch (e) {
    console.error(
      `Failed to disconnect connection ${requestedConnectionId} for project ${requestedProjectId} in org ${organizationId}:`,
      e
    );

    logErrorToAnalytics(
      c.env,
      "CONNECTION_DISCONNECT_ERROR",
      "Failed to disconnect connection",
      {
        connectionId: requestedConnectionId,
        projectId: requestedProjectId,
        organizationId,
        error: String(e),
      }
    );

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to disconnect connection",
    });
  }
};
