import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { and, eq } from "drizzle-orm";
import { BlankInput } from "hono/types";
import { unsubscribeFromYouTubeChannel } from "../../../../platforms/youtube-pubsub";

export const deleteConnection = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections/:connectionId",
    BlankInput
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "Authentification failed" });

  const projectId = c.req.param("projectId");
  const connectionId = c.req.param("connectionId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .innerJoin(
        schema.platformConnections,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId),
          eq(schema.platformConnections.id, connectionId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, {
        message: "Project or Connection not found",
      });
    }

    // Get connection details before deletion for PubSub cleanup
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    // Unsubscribe from PubSub notifications if it's a YouTube connection
    if (
      connection &&
      connection.platform === "youtube" &&
      connection.platformAccountId
    ) {
      try {
        await unsubscribeFromYouTubeChannel(
          connection.platformAccountId,
          c.env
        );
        console.log(
          `Unsubscribed from YouTube channel: ${connection.platformAccountId}`
        );
      } catch (unsubscribeError) {
        console.error(
          "Failed to unsubscribe from YouTube channel:",
          unsubscribeError
        );
        // Don't fail the deletion if unsubscription fails
        logErrorToAnalytics(
          c.env,
          "YOUTUBE_UNSUBSCRIBE_ERROR",
          "Failed to unsubscribe from YouTube channel during deletion",
          {
            connectionId,
            channelId: connection.platformAccountId,
            error: String(unsubscribeError),
          }
        );
      }
    }

    await db.batch([
      db
        .delete(schema.posts)
        .where(eq(schema.posts.platformConnectionId, connectionId)),
      db
        .delete(schema.generatedLinks)
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId)),
      db
        .delete(schema.feedConnections)
        .where(eq(schema.feedConnections.platformConnectionId, connectionId)),
      db
        .delete(schema.platformConnections)
        .where(eq(schema.platformConnections.id, connectionId)),
    ]);

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to delete connection ${connectionId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "CONNECTION_DELETE_ERROR",
      `Failed to delete connection`,
      {
        connectionId,
        projectId,
        organizationId,
        error: String(e),
      }
    );
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to delete connection",
    });
  }
};
