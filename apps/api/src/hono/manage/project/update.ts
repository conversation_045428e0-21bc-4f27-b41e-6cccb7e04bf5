import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { fixUserAndOrgTable } from "../../manage/account/fix";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

const validator = z.object({
  name: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  isActive: z.boolean().optional().nullable(),
});

export const updateProjectValidator = zValidator("json", validator);

export const updateProject = async (
  c: Context<
    AppContext,
    "/manage/projects",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");
  const name = validated.name;
  const description = validated.description;
  const isActive = validated.isActive;
  const userId = c.var.user?.userId;

  if (!name || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const projectId = c.req.param("projectId");

  if (!projectId)
    throw new HTTPException(400, { message: "Project ID required" });

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(404, { message: "Project not found" });
    }

    await db
      .update(schema.projects)
      .set({
        ...(name && { name }),
        ...(description && { description }),
        ...(isActive !== undefined &&
          isActive !== null && { isActive: isActive }),
        updatedAt: new Date(),
      })
      .where(eq(schema.projects.id, projectId))
      .run();

    return c.json({ success: true });
  } catch (e) {
    console.error(`Failed to add feed for org ${organizationId}:`, e);
    logErrorToAnalytics(
      c.env,
      "PROJECT_UPDATE_ERROR",
      `Failed to update project`,
      {
        projectId,
        organizationId,
        error: String(e),
      }
    );
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to update project",
    });
  }
};
