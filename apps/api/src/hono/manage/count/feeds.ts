import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const getFeedsCount = async (
  c: Context<AppContext, "/manage/count/projects/:projectId/feeds", BlankInput>
) => {
  const projectId = c.req.param("projectId");
  const organizationId = c.var.organizationId;
  if (!organizationId) {
    throw new HTTPException(403, {
      message: "Organization context required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    const data = await db
      .select({
        count: sql<number>`count(*)`,
        activeCount: sql<number>`count(case when feeds.is_active = 1 then 1 end)`,
      })
      .from(schema.feeds)
      .leftJoin(schema.projects, eq(schema.feeds.projectId, schema.projects.id))
      .where(
        and(
          eq(schema.projects.organizationId, organizationId),
          eq(schema.feeds.projectId, projectId)
        )
      )
      .get();

    return c.json({
      feeds: data?.count ?? 0,
      activeFeeds: data?.activeCount ?? 0,
    });
  } catch (e) {
    console.error(
      `Failed to fetch projects count for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "FEEDS_COUNT_FETCH_ERROR",
      `Failed to count feed_plattform_connections`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to count feeds",
    });
  }
};
export default getFeedsCount;
