// src/hono/oauth/meta/callback.ts
import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { AppContext } from "../../../types";
import { revalidateConnection } from "../../../revalidation-utils";
import { triggerInitialSync } from "../../../sync-utils";
import { getPlatformAdapter } from "../../../platforms";

const GraphApiPagesResponseSchema = z.object({
  data: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      access_token: z.string(),
      instagram_business_account: z
        .object({
          id: z.string(),
        })
        .optional(),
    })
  ),
});

const InstagramUserResponseSchema = z.object({
  id: z.string(),
  username: z.string(),
});

const FacebookPageDetailsSchema = z.object({
  about: z.string().optional(),
  fan_count: z.number().optional(),
  picture: z
    .object({
      data: z.object({
        url: z.string().url(),
      }),
    })
    .optional(),
});

export const metaCallbackHandler = async (c: Context<AppContext>) => {
  const code = c.req.query("code");
  const state = c.req.query("state");
  const error = c.req.query("error");

  console.log("Meta OAuth callback received", { code, state, error });

  if (error) {
    console.error("Meta OAuth error:", error);
    return c.redirect(
      `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=${error}`
    );
  }

  if (!code || !state) {
    throw new HTTPException(400, {
      message: "Missing code or state parameter",
    });
  }

  try {
    // Decode state to get connection ID and redirect info
    const stateData = JSON.parse(decodeURIComponent(state));
    const { connectionId, token } = stateData;

    if (!connectionId) {
      throw new HTTPException(400, { message: "Invalid state parameter" });
    }

    const db = getDbClient(c.env.DB);

    // Get connection details
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    const platformAdapter = getPlatformAdapter(connection.platform);

    const { accessToken, expiresAt } = await platformAdapter.exchangeToken(
      code,
      c.env
    );

    // Encrypt the access token
    const encryptedAccessToken = await encryptToken(
      accessToken,
      c.env.ENCRYPTION_KEY
    );

    // Handle Facebook page selection flow
    if (connection.platform === "facebook") {
      // For Facebook, we need to get available pages and set up page selection
      const { getFacebookPages } = await import("../../../platforms/facebook");
      const availablePages = await getFacebookPages(accessToken, c.env);

      if (availablePages.length === 0) {
        throw new HTTPException(400, {
          message:
            "No Facebook Pages found. You need to manage at least one Facebook Page to use this integration.",
        });
      }

      // Update connection with pending page selection state
      await db
        .update(schema.platformConnections)
        .set({
          accessTokenEncrypted: encryptedAccessToken,
          tokenExpiresAt: expiresAt,
          isConnected: false, // Not fully connected until page is selected
          pendingPageSelection: true,
          availablePages: JSON.stringify(availablePages),
          isActive: true,
          hasError: false,
          needsReconnect: false,
          lastCheckedAt: new Date(),
          platform: connection.platform,
        })
        .where(eq(schema.platformConnections.id, connectionId));

      // Redirect to page selection instead of dashboard
      let redirectUrl: string;
      if (token) {
        redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/select-page`;
      } else {
        redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}/select-page`;
      }
      console.log("WE ARE HERE", redirectUrl);
      return c.redirect(redirectUrl);
    }

    // For other platforms, get user info as before
    const userInfo = await platformAdapter.getPlatformInformation(
      { ...connection, accessTokenEncrypted: encryptedAccessToken },
      c.env
    );

    if (!userInfo) {
      throw new HTTPException(400, {
        message: "Failed to get user info after token exchange.",
      });
    }

    // Update connection in database
    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: userInfo.id,
        platformAccountName: userInfo.name ?? userInfo.username,
        platformAccountUsername: userInfo.username,
        platformAccountDescription: userInfo.description,
        platformAccountFollowers: userInfo.followers_count,
        platformAccountFollowing: userInfo.follows_count,
        platformAccountProfilePictureUrl: userInfo.profile_picture_url,
        platformPostCount: userInfo.posts_count,
        accessTokenEncrypted: encryptedAccessToken,
        tokenExpiresAt: expiresAt,
        isConnected: true,
        isActive: true,
        hasError: false,
        needsReconnect: false,
        lastCheckedAt: new Date(),
        platform: connection.platform,
      })
      .where(eq(schema.platformConnections.id, connectionId));

    // All updates for the instagram business case are already done.

    // Trigger initial post synchronization
    await triggerInitialSync(c.env, connection.id);

    // Invalidate all existing generated links for this connection
    // since the connection is now established and future links should use replace flow
    try {
      const invalidateResult = await db
        .update(schema.generatedLinks)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId));

      console.log(
        "Meta: Invalidated existing generated links:",
        invalidateResult
      );
    } catch (invalidateError) {
      console.error(
        "Meta: Failed to invalidate existing links:",
        invalidateError
      );
      // Don't fail the OAuth flow if link invalidation fails
      logErrorToAnalytics(
        c.env,
        "META_LINK_INVALIDATION_ERROR",
        "Failed to invalidate existing generated links",
        { connectionId, error: String(invalidateError) }
      );
    }

    // Trigger cache revalidation for the connection page
    if (c.env.REVALIDATION_SECRET) {
      try {
        await revalidateConnection(
          c.env.NEXT_PUBLIC_WEB_URL,
          c.env.REVALIDATION_SECRET,
          connection.projectId,
          connectionId
        );
        console.log("Meta: Cache revalidation triggered successfully");
      } catch (revalidationError) {
        console.error("Meta: Cache revalidation failed:", revalidationError);
        // Don't fail the OAuth flow if revalidation fails
      }
    }

    // Redirect based on flow type
    let redirectUrl: string;

    if (token) {
      // Public link flow - redirect to public success page
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/success`;
    } else {
      // Authenticated flow - redirect to dashboard
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?success=true`;
    }

    return c.redirect(redirectUrl);
  } catch (error: any) {
    console.error("Meta OAuth callback error:", error);
    logErrorToAnalytics(
      c.env,
      "META_OAUTH_ERROR",
      "Meta OAuth callback failed",
      { error: String(error) }
    );

    const redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=oauth_failed`;
    return c.redirect(redirectUrl);
  }
};
