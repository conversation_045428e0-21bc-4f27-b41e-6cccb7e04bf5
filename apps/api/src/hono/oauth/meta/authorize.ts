// src/hono/oauth/meta/authorize.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

export const metaAuthorizeHandler = async (c: Context<AppContext>) => {
  const connectionId = c.req.query("connection_id");
  const token = c.req.query("token"); // Optional link token
  const type = c.req.query("type"); // 'instagram_business_login' or 'instagram_facebook_login'
  const isReconnect = c.req.query("reconnect") === "true";

  if (!connectionId && !token) {
    throw new HTTPException(400, {
      message: "Missing connection_id or token parameter",
    });
  }

  try {
    const db = getDbClient(c.env.DB);
    let connection;
    let actualConnectionId = connectionId;

    if (connectionId) {
      // Direct connection ID provided
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, connectionId))
        .get();
    } else if (token) {
      // Find connection via generated link token
      const link = await db
        .select({
          platformConnectionId: schema.generatedLinks.platformConnectionId,
        })
        .from(schema.generatedLinks)
        .where(eq(schema.generatedLinks.token, token))
        .get();

      if (!link) {
        throw new HTTPException(404, {
          message: "Invalid or expired link token",
        });
      }

      actualConnectionId = link.platformConnectionId;
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, actualConnectionId))
        .get();
    }

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    // Validate platform is Meta-based and update based on type
    if (
      ![
        "instagram",
        "instagram_business",
        "instagram_with_facebook",
        "facebook",
      ].includes(connection.platform)
    ) {
      throw new HTTPException(400, {
        message: "Invalid platform for Meta OAuth",
      });
    }

    // Create state parameter with connection info
    const state = encodeURIComponent(
      JSON.stringify({
        connectionId: actualConnectionId,
        token: token || null,
        timestamp: Date.now(),
      })
    );

    // Determine API type and scopes based on type parameter
    let scopes: string[];
    let authUrl: URL;

    console.log("Meta OAuth type:", type, connection.platform);

    if (type === "instagram_business_login") {
      scopes = ["instagram_business_basic"];
      authUrl = new URL("https://www.instagram.com/oauth/authorize");
      authUrl.searchParams.set("client_id", c.env.INSTAGRAM_BUSINESS_APP_ID);
    } else if (
      type === "instagram_facebook_login" ||
      type === "facebook_login"
    ) {
      if (
        connection.platform === "instagram_business" ||
        connection.platform === "instagram"
      ) {
        scopes = [
          "instagram_basic",
          "pages_read_engagement",
          "pages_show_list",
        ];
      } else {
        // facebook
        scopes = [
          "pages_show_list",
          "pages_read_engagement",
          "pages_read_user_content",
        ];
      }
      authUrl = new URL("https://www.facebook.com/v18.0/dialog/oauth");
      authUrl.searchParams.set("client_id", c.env.FACEBOOK_APP_ID);
      if (type === "instagram_facebook_login") {
        authUrl.searchParams.set(
          "config_id",
          c.env.INSTAGRAM_VIA_FACEBOOK_CONFIG_ID
        );
      } else if (type === "facebook_login") {
        authUrl.searchParams.set("config_id", c.env.FACEBOOK_CONFIG_ID);
      }
    } else {
      throw new HTTPException(400, {
        message: "Invalid or missing 'type' parameter.",
      });
    }

    if (isReconnect) {
      authUrl.searchParams.set("auth_type", "reauthenticate");
    }

    authUrl.searchParams.set(
      "redirect_uri",
      `${c.env.NEXT_PUBLIC_API_URL}/oauth/meta/callback`
    );
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", scopes.join(","));

    authUrl.searchParams.set("state", state);

    return c.redirect(authUrl.toString());
  } catch (error: any) {
    console.error("Meta OAuth authorization error:", error);

    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(500, {
      message: "Failed to initiate Meta OAuth",
    });
  }
};
