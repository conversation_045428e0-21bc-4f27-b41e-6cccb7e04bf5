// src/hono/oauth/youtube/callback.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { revalidateConnection } from "../../../revalidation-utils";
import {
  subscribeToYouTubeChannel,
  unsubscribeFromYouTubeChannel,
} from "../../../platforms/youtube-pubsub";
import { triggerInitialSync } from "../../../sync-utils";

const YouTubeTokenResponseSchema = z.object({
  access_token: z.string(),
  refresh_token: z.string().optional(),
  expires_in: z.number(),
  scope: z.string(),
  token_type: z.string(),
});

const YouTubeChannelResponseSchema = z.object({
  items: z.array(
    z.object({
      id: z.string(),
      snippet: z.object({
        title: z.string(),
        description: z.string().optional(),
        customUrl: z.string().optional(),
        thumbnails: z.object({
          default: z.object({ url: z.string().url() }).optional(),
          medium: z.object({ url: z.string().url() }).optional(),
          high: z.object({ url: z.string().url() }).optional(),
        }),
      }),
      statistics: z.object({
        viewCount: z.string(),
        subscriberCount: z.string(),
        hiddenSubscriberCount: z.boolean(),
        videoCount: z.string(),
      }),
    })
  ),
});

export const youtubeCallbackHandler = async (c: Context<AppContext>) => {
  const code = c.req.query("code");
  const state = c.req.query("state");
  const error = c.req.query("error");

  console.log("YOUTUBE CALLBACK", { code, state, error });

  if (error) {
    console.error("YouTube OAuth error:", error);

    // Try to get connection details from state for better redirect
    try {
      if (state) {
        const stateData = JSON.parse(decodeURIComponent(state));
        const { connectionId, token } = stateData;

        if (connectionId) {
          const db = getDbClient(c.env.DB);
          const connection = await db
            .select()
            .from(schema.platformConnections)
            .where(eq(schema.platformConnections.id, connectionId))
            .get();

          if (connection) {
            const redirectUrl = token
              ? `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?token=${token}&error=${error}`
              : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?error=${error}`;
            return c.redirect(redirectUrl);
          }
        }
      }
    } catch (redirectError) {
      console.error(
        "Failed to construct specific error redirect:",
        redirectError
      );
    }

    // Fallback to generic error page
    return c.redirect(
      `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=${error}`
    );
  }

  if (!code || !state) {
    throw new HTTPException(400, {
      message: "Missing code or state parameter",
    });
  }

  try {
    // Decode state to get connection ID and redirect info
    const stateData = JSON.parse(decodeURIComponent(state));
    const { connectionId, token } = stateData;

    if (!connectionId) {
      throw new HTTPException(400, { message: "Invalid state parameter" });
    }

    // Exchange code for access token
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        code,
        client_id: c.env.GOOGLE_CLIENT_ID,
        client_secret: c.env.GOOGLE_CLIENT_SECRET,
        redirect_uri: `${c.env.NEXT_PUBLIC_API_URL}/oauth/youtube/callback`,
        grant_type: "authorization_code",
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error("YouTube token exchange failed:", errorText);
      throw new HTTPException(400, { message: "Token exchange failed" });
    }

    const tokenData = await tokenResponse.json();
    const validation = YouTubeTokenResponseSchema.safeParse(tokenData);

    if (!validation.success) {
      console.error("Invalid token response:", validation.error);
      throw new HTTPException(400, { message: "Invalid token response" });
    }

    const { access_token, refresh_token, expires_in, scope } = validation.data;

    // Get channel information
    const channelResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics,brandingSettings&mine=true&access_token=${access_token}`
    );

    if (!channelResponse.ok) {
      throw new HTTPException(400, { message: "Failed to fetch channel info" });
    }

    const channelData = await channelResponse.json();
    const channelValidation =
      YouTubeChannelResponseSchema.safeParse(channelData);

    if (
      !channelValidation.success ||
      channelValidation.data.items.length === 0
    ) {
      throw new HTTPException(400, { message: "No YouTube channel found" });
    }

    const channel = channelValidation.data.items[0];
    const channelId = channel.id;
    const channelTitle = channel.snippet.title;
    const channelDescription = channel.snippet.description;
    const subscriberCount = channel.statistics.subscriberCount;
    const postCount = channel.statistics.videoCount;
    const profilePictureUrl = channel.snippet.thumbnails.high?.url;

    // Encrypt tokens
    console.log("YouTube: Encrypting access token...");
    const encryptedAccessToken = await encryptToken(
      access_token,
      c.env.ENCRYPTION_KEY
    );
    console.log("YouTube: Access token encrypted:", !!encryptedAccessToken);

    if (!encryptedAccessToken) {
      console.error("YouTube: Failed to encrypt access token!");
      throw new HTTPException(500, {
        message: "Failed to encrypt access token",
      });
    }

    const encryptedRefreshToken = refresh_token
      ? await encryptToken(refresh_token, c.env.ENCRYPTION_KEY)
      : null;
    console.log("YouTube: Refresh token encrypted:", !!encryptedRefreshToken);

    // Get connection details for redirect and PubSub management
    const db = getDbClient(c.env.DB);
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    // Store old channel ID for PubSub unsubscription (if this is a replacement)
    const oldChannelId = connection.platformAccountId;

    // Unsubscribe from old channel's PubSub if this is a replacement
    if (oldChannelId && oldChannelId !== channelId) {
      try {
        console.log(`YouTube: Unsubscribing from old channel: ${oldChannelId}`);
        await unsubscribeFromYouTubeChannel(oldChannelId, c.env);
        console.log(
          `YouTube: Successfully unsubscribed from old channel: ${oldChannelId}`
        );
      } catch (unsubscribeError) {
        console.error(
          "YouTube: Failed to unsubscribe from old channel:",
          unsubscribeError
        );
        // Don't fail the OAuth flow if unsubscription fails
        logErrorToAnalytics(
          c.env,
          "YOUTUBE_OLD_CHANNEL_UNSUBSCRIBE_ERROR",
          "Failed to unsubscribe from old YouTube channel during replacement",
          {
            connectionId,
            oldChannelId,
            newChannelId: channelId,
            error: String(unsubscribeError),
          }
        );
      }
    }

    // Update connection in database
    const expiresAt = new Date(Date.now() + expires_in * 1000);

    console.log("YouTube: Updating database with connection data...");
    console.log("YouTube: Connection ID:", connectionId);
    console.log("YouTube: Channel ID:", channelId);
    console.log("YouTube: Channel Title:", channelTitle);
    console.log("YouTube: Has encrypted access token:", !!encryptedAccessToken);

    const updateResult = await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: channelId,
        platformAccountName: channelTitle,
        platformAccountDescription: channelDescription,
        platformAccountFollowers: parseInt(subscriberCount, 10),
        platformPostCount: parseInt(postCount, 10),
        platformAccountProfilePictureUrl: profilePictureUrl,
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        tokenExpiresAt: expiresAt,
        isConnected: true,
        isActive: true,
        hasError: false,
        needsReconnect: false,
        lastCheckedAt: new Date(),
        scopes: scope,
      })
      .where(eq(schema.platformConnections.id, connectionId));

    console.log("YouTube: Database update result:", updateResult);

    // Trigger initial post synchronization
    await triggerInitialSync(c.env, connectionId);

    // Invalidate all existing generated links for this connection
    // since the connection is now established and future links should use replace flow
    try {
      const invalidateResult = await db
        .update(schema.generatedLinks)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId));

      console.log(
        "YouTube: Invalidated existing generated links:",
        invalidateResult
      );
    } catch (invalidateError) {
      console.error(
        "YouTube: Failed to invalidate existing links:",
        invalidateError
      );
      // Don't fail the OAuth flow if link invalidation fails
      logErrorToAnalytics(
        c.env,
        "YOUTUBE_LINK_INVALIDATION_ERROR",
        "Failed to invalidate existing generated links",
        { connectionId, error: String(invalidateError) }
      );
    }

    // Subscribe to YouTube PubSubHubbub for this channel
    try {
      const isReplacement = oldChannelId && oldChannelId !== channelId;
      console.log(
        `YouTube: Subscribing to ${isReplacement ? "replacement" : "new"} channel: ${channelId}`
      );
      await subscribeToYouTubeChannel(channelId, c.env);
      console.log(`YouTube: Successfully subscribed to channel: ${channelId}`);
    } catch (subscribeError) {
      console.error("Failed to subscribe to YouTube channel:", subscribeError);
      // Don't fail the OAuth flow if subscription fails
      logErrorToAnalytics(
        c.env,
        "YOUTUBE_SUBSCRIPTION_ERROR",
        "Failed to subscribe to YouTube channel",
        {
          channelId,
          oldChannelId,
          isReplacement: oldChannelId && oldChannelId !== channelId,
          error: String(subscribeError),
        }
      );
    }

    // Trigger cache revalidation for the connection page
    if (c.env.REVALIDATION_SECRET) {
      try {
        await revalidateConnection(
          c.env.NEXT_PUBLIC_WEB_URL,
          c.env.REVALIDATION_SECRET,
          connection.projectId,
          connectionId
        );
        console.log("YouTube: Cache revalidation triggered successfully");
      } catch (revalidationError) {
        console.error("YouTube: Cache revalidation failed:", revalidationError);
        // Don't fail the OAuth flow if revalidation fails
      }
    }

    // Redirect based on flow type
    let redirectUrl: string;

    if (token) {
      // Public link flow - redirect to public success page
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/success`;
    } else {
      // Authenticated flow - redirect to dashboard
      redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?success=true`;
    }

    return c.redirect(redirectUrl);
  } catch (error: any) {
    console.error("YouTube OAuth callback error:", error);
    logErrorToAnalytics(
      c.env,
      "YOUTUBE_OAUTH_ERROR",
      "YouTube OAuth callback failed",
      { error: String(error) }
    );

    // Try to get connection details for better redirect, fallback to generic error page
    try {
      const stateData = JSON.parse(decodeURIComponent(state || "{}"));
      const { connectionId, token } = stateData;

      if (connectionId) {
        const db = getDbClient(c.env.DB);
        const connection = await db
          .select()
          .from(schema.platformConnections)
          .where(eq(schema.platformConnections.id, connectionId))
          .get();

        if (connection) {
          const redirectUrl = token
            ? `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?token=${token}&error=oauth_failed`
            : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?error=oauth_failed`;
          return c.redirect(redirectUrl);
        }
      }
    } catch (redirectError) {
      console.error(
        "Failed to construct specific error redirect:",
        redirectError
      );
    }

    // Fallback to generic error page
    const redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=oauth_failed`;
    return c.redirect(redirectUrl);
  }
};
