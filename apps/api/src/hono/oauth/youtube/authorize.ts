// src/hono/oauth/youtube/authorize.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

export const youtubeAuthorizeHandler = async (c: Context<AppContext>) => {
  const connectionId = c.req.query("connection_id");
  const token = c.req.query("token"); // Optional link token

  if (!connectionId && !token) {
    throw new HTTPException(400, {
      message: "Missing connection_id or token parameter",
    });
  }

  try {
    const db = getDbClient(c.env.DB);
    let connection;
    let actualConnectionId = connectionId;

    if (connectionId) {
      // Direct connection ID provided
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, connectionId))
        .get();
    } else if (token) {
      // Find connection via generated link token
      const link = await db
        .select({
          platformConnectionId: schema.generatedLinks.platformConnectionId,
        })
        .from(schema.generatedLinks)
        .where(eq(schema.generatedLinks.token, token))
        .get();

      if (!link) {
        throw new HTTPException(404, {
          message: "Invalid or expired link token",
        });
      }

      actualConnectionId = link.platformConnectionId;
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, actualConnectionId))
        .get();
    }

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    if (connection.platform !== "youtube") {
      throw new HTTPException(400, {
        message: "Invalid platform for YouTube OAuth",
      });
    }

    // Create state parameter with connection info
    const state = encodeURIComponent(
      JSON.stringify({
        connectionId: actualConnectionId,
        token: token || null,
        timestamp: Date.now(),
      })
    );

    // YouTube OAuth scopes
    const scopes = [
      "https://www.googleapis.com/auth/youtube.readonly",
      "https://www.googleapis.com/auth/youtube.channel-memberships.creator",
    ].join(" ");

    // Build YouTube OAuth URL
    const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
    authUrl.searchParams.set("client_id", c.env.GOOGLE_CLIENT_ID);
    authUrl.searchParams.set(
      "redirect_uri",
      `${c.env.NEXT_PUBLIC_API_URL}/oauth/youtube/callback`
    );
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", scopes);
    authUrl.searchParams.set("state", state);
    authUrl.searchParams.set("access_type", "offline");
    authUrl.searchParams.set("prompt", "consent");
    authUrl.searchParams.set("access_type", "offline");
    authUrl.searchParams.set("prompt", "consent");

    return c.redirect(authUrl.toString());
  } catch (error: any) {
    console.error("YouTube OAuth authorization error:", error);

    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(500, {
      message: "Failed to initiate YouTube OAuth",
    });
  }
};
