// src/hono/oauth/tiktok/authorize.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

export const tiktokAuthorizeHandler = async (c: Context<AppContext>) => {
  const connectionId = c.req.query("connection_id");
  const token = c.req.query("token"); // Optional link token from generated links

  if (!connectionId && !token) {
    throw new HTTPException(400, {
      message: "Missing connection_id or token parameter",
    });
  }

  try {
    const db = getDbClient(c.env.DB);
    let connection: any;
    let actualConnectionId = connectionId as string | undefined;

    if (connectionId) {
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, connectionId))
        .get();
    } else if (token) {
      const link = await db
        .select({
          platformConnectionId: schema.generatedLinks.platformConnectionId,
        })
        .from(schema.generatedLinks)
        .where(eq(schema.generatedLinks.token, token))
        .get();

      if (!link) {
        throw new HTTPException(404, {
          message: "Invalid or expired link token",
        });
      }

      actualConnectionId = link.platformConnectionId;
      connection = await db
        .select()
        .from(schema.platformConnections)
        .where(eq(schema.platformConnections.id, actualConnectionId))
        .get();
    }

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    if (connection.platform !== "tiktok") {
      throw new HTTPException(400, {
        message: "Invalid platform for TikTok OAuth",
      });
    }

    // Generate PKCE code verifier and challenge
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = await generateCodeChallenge(codeVerifier);

    // Create state parameter with connection info and code verifier
    const state = encodeURIComponent(
      JSON.stringify({
        connectionId: actualConnectionId,
        token: token || null,
        timestamp: Date.now(),
        codeVerifier, // Store code verifier in state for later use
      })
    );

    // TikTok Login Kit scopes for reading profile and public videos + stats
    const scopes = ["user.info.basic", "video.list", "user.info.stats"].join(
      ","
    );

    // Build TikTok OAuth URL (Login Kit v2)
    const authUrl = new URL("https://www.tiktok.com/v2/auth/authorize/");
    authUrl.searchParams.set("client_key", c.env.TIKTOK_CLIENT_KEY);
    authUrl.searchParams.set(
      "redirect_uri",
      `${c.env.NEXT_PUBLIC_API_URL}/oauth/tiktok/callback`
    );
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", scopes);
    authUrl.searchParams.set("state", state);
    authUrl.searchParams.set("code_challenge", codeChallenge);
    authUrl.searchParams.set("code_challenge_method", "S256");

    return c.redirect(authUrl.toString());
  } catch (error: any) {
    console.error("TikTok OAuth authorization error:", error);

    if (error instanceof HTTPException) {
      throw error;
    }

    throw new HTTPException(500, {
      message: "Failed to initiate TikTok OAuth",
    });
  }
};

// Generate a random code verifier for PKCE
function generateCodeVerifier(): string {
  const buffer = new Uint8Array(32);
  crypto.getRandomValues(buffer);
  return base64URLEncode(buffer);
}

// Generate code challenge from code verifier
async function generateCodeChallenge(codeVerifier: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(codeVerifier);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return base64URLEncode(new Uint8Array(hashBuffer));
}

// Base64 URL encode a buffer
function base64URLEncode(buffer: Uint8Array): string {
  let binary = "";
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
}
