// src/hono/oauth/tiktok/callback.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { revalidateConnection } from "../../../revalidation-utils";
import { triggerInitialSync } from "../../../sync-utils";
import { z } from "zod";

const TikTokUserSchema = z.object({
  open_id: z.string(),
  display_name: z.string().optional(),
  nickname: z.string().optional(),
  bio_description: z.string().optional(),
  follower_count: z.number().optional(),
  following_count: z.number().optional(),
  avatar_url: z.string().url().optional(),
});

export const tiktokCallbackHandler = async (c: Context<AppContext>) => {
  const code = c.req.query("code");
  const state = c.req.query("state");
  const error = c.req.query("error");

  if (error) {
    console.error("TikTok OAuth error:", error);
    return c.redirect(
      `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects?error=${error}`
    );
  }

  if (!code || !state) {
    throw new HTTPException(400, {
      message: "Missing code or state parameter",
    });
  }

  try {
    const stateData = JSON.parse(decodeURIComponent(state));
    const { connectionId, token, codeVerifier } = stateData;

    if (!connectionId) {
      throw new HTTPException(400, { message: "Invalid state parameter" });
    }

    const db = getDbClient(c.env.DB);

    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    if (!connection) {
      throw new HTTPException(404, { message: "Connection not found" });
    }

    // Exchange code for access_token (TikTok v2 token endpoint)
    const tokenResp = await fetch(
      "https://open.tiktokapis.com/v2/oauth/token/",
      {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
          client_key: c.env.TIKTOK_CLIENT_KEY,
          client_secret: c.env.TIKTOK_CLIENT_SECRET,
          code,
          grant_type: "authorization_code",
          redirect_uri: `${c.env.NEXT_PUBLIC_API_URL}/oauth/tiktok/callback`,
          code_verifier: codeVerifier,
        }),
      }
    );

    if (!tokenResp.ok) {
      const text = await tokenResp.text();
      console.error("TikTok token exchange failed:", text);
      throw new HTTPException(400, { message: "Token exchange failed" });
    }

    const tokenJson = await tokenResp.json<any>();
    const accessToken = tokenJson.access_token as string;
    const refreshToken = (tokenJson.refresh_token as string) || null;
    const expiresIn = (tokenJson.expires_in as number) || null;

    if (!accessToken) {
      throw new HTTPException(400, { message: "Invalid token response" });
    }

    // Fetch user info to get open_id and display_name
    const userInfoResp = await fetch(
      "https://open.tiktokapis.com/v2/user/info/?fields=open_id,display_name,nickname,bio_description,follower_count,following_count,avatar_url",
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    if (!userInfoResp.ok) {
      const txt = await userInfoResp.text();
      console.error("TikTok user info failed:", txt);
      throw new HTTPException(400, { message: "Failed to get user info" });
    }

    const userInfoData: { data?: { user?: any } } = await userInfoResp.json();
    const userInfoValidation = TikTokUserSchema.safeParse(
      userInfoData?.data?.user
    );

    if (!userInfoValidation.success) {
      console.error("Invalid TikTok user info:", userInfoValidation.error);
      throw new HTTPException(400, {
        message: "Invalid TikTok user info response",
      });
    }

    const {
      open_id: openId,
      display_name,
      nickname,
      bio_description,
      follower_count,
      following_count,
      avatar_url,
    } = userInfoValidation.data;
    const displayName = display_name || nickname || "TikTok User";

    const encryptedAccessToken = await encryptToken(
      accessToken,
      c.env.ENCRYPTION_KEY
    );
    const encryptedRefreshToken = refreshToken
      ? await encryptToken(refreshToken, c.env.ENCRYPTION_KEY)
      : null;

    const expiresAt = expiresIn
      ? new Date(Date.now() + expiresIn * 1000)
      : null;

    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: openId,
        platformAccountName: displayName,
        platformAccountDescription: bio_description,
        platformAccountFollowers: follower_count,
        platformAccountFollowing: following_count,
        platformAccountProfilePictureUrl: avatar_url,
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        tokenExpiresAt: expiresAt,
        isConnected: true,
        isActive: true,
        hasError: false,
        needsReconnect: false,
        lastCheckedAt: new Date(),
      })
      .where(eq(schema.platformConnections.id, connectionId));

    // Trigger initial post synchronization
    await triggerInitialSync(c.env, connectionId);

    // Invalidate generated link if present
    if (token) {
      await db
        .update(schema.generatedLinks)
        .set({ isActive: false, updatedAt: new Date() })
        .where(eq(schema.generatedLinks.platformConnectionId, connectionId));
    }

    // Trigger revalidation hooks (if used)
    try {
      await revalidateConnection(
        c.env.NEXT_PUBLIC_WEB_URL,
        c.env.REVALIDATION_SECRET || "",
        connection.projectId,
        connectionId
      );
    } catch {}

    // Redirect to success
    const redirectUrl = token
      ? `${c.env.NEXT_PUBLIC_WEB_URL}/connect/${token}/success`
      : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?success=true`;

    return c.redirect(redirectUrl);
  } catch (error: any) {
    console.error("TikTok OAuth callback error:", error);
    logErrorToAnalytics(
      c.env,
      "TIKTOK_OAUTH_ERROR",
      "TikTok OAuth callback failed",
      {
        error: String(error),
      }
    );

    try {
      const stateData = JSON.parse(decodeURIComponent(state || "{}"));
      const { connectionId, token } = stateData || {};
      if (connectionId) {
        const db = getDbClient(c.env.DB);
        const connection = await db
          .select()
          .from(schema.platformConnections)
          .where(eq(schema.platformConnections.id, connectionId))
          .get();

        if (connection) {
          const redirectUrl = token
            ? `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?token=${token}&error=oauth_failed`
            : `${c.env.NEXT_PUBLIC_WEB_URL}/dashboard/projects/${connection.projectId}/connections/${connectionId}?error=oauth_failed`;
          return c.redirect(redirectUrl);
        }
      }
    } catch {}

    throw new HTTPException(500, { message: "TikTok OAuth failed" });
  }
};
