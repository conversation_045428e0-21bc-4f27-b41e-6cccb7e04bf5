import { Context } from "hono";
import { AppContext, Platform, Post } from "../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { logErrorToAnalytics } from "../../analytics-utils";
import { getDbClient, getPostsForFeed } from "../../database-service";

const feedPostsHandler = async (
  c: Context<AppContext, "/feed/:feedId/posts", BlankInput>
) => {
  const requestedFeedId = c.req.param("feedId");
  const authorizedFeedId = c.var.authorizedFeedId;
  if (!authorizedFeedId || requestedFeedId !== authorizedFeedId)
    throw new HTTPException(403, {
      message: "Forbidden: API key not authorized for this feed",
    });
  const apiKey = c.var.apiKey;

  // Analytics Zählung
  if (c.env.USAGE_ANALYTICS) {
    try {
      c.executionCtx.waitUntil(
        Promise.resolve()
          .then(() => {
            c.env.USAGE_ANALYTICS.writeDataPoint({
              indexes: [authorizedFeedId],
              blobs: [`/feeds/${requestedFeedId}/posts`, apiKey || ""],
              doubles: [1],
            });
            console.log(`Logged API usage for feed ${authorizedFeedId}`);
          })
          .catch((e) => console.error("Analytics Engine write failed:", e))
      );
    } catch (e) {
      console.error("Error calling Analytics Engine:", e);
    }
  }

  let postsData: Post[] | null = null;
  let cacheStatus: "hit" | "miss" = "miss"; // Um den Header am Ende korrekt zu setzen

  // Cache Logik
  const cache = await caches.open("api-cache");
  const cacheKey = new Request(c.req.url, c.req);
  try {
    const cachedResponse = await cache.match(cacheKey);
    if (cachedResponse) {
      console.log(`Cache HIT for: ${c.req.url}`);
      // Wir parsen die JSON-Daten aus der gecachten Response
      postsData = await cachedResponse.json<Post[]>();
      cacheStatus = "hit";
    }
  } catch (cacheError) {
    console.error(`Cache match/parse error for ${c.req.url}:`, cacheError);
    logErrorToAnalytics(
      c.env,
      "CACHE_MATCH_ERROR",
      "Cache match/parse failed",
      {
        url: c.req.url,
        error: String(cacheError),
      }
    );
  }
  // 3. DB-Fetch-Logik (nur ausführen, wenn der Cache "miss" war)
  if (postsData === null) {
    console.log(`Cache MISS for: ${c.req.url}`);
    cacheStatus = "miss";
    try {
      const db = getDbClient(c.env.DB);
      const limit = Math.min(
        Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
        100
      );
      const platformFilter = c.req.query("platform") as Platform | undefined;

      // Die Ergebnisse aus der DB werden in unserer Variable gespeichert
      postsData = await getPostsForFeed(
        db,
        authorizedFeedId,
        limit,
        platformFilter
      );

      // Cache im Hintergrund aktualisieren. Wir cachen die reinen JSON-Daten.
      // Dafür erstellen wir eine temporäre Response, die nur die Daten enthält.
      const responseToCache = new Response(JSON.stringify(postsData), {
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=30", // Increase cache TTL to 30 seconds for more aggressive caching
        },
      });
      c.executionCtx.waitUntil(cache.put(cacheKey, responseToCache));
    } catch (e: any) {
      console.error(`Error fetching posts for feed ${authorizedFeedId}:`, e);
      logErrorToAnalytics(c.env, "GET_POSTS_ERROR", "Failed to fetch posts", {
        feedId: authorizedFeedId,
        error: String(e),
      });
      throw new HTTPException(500, { message: "Failed to fetch posts" });
    }
  }

  // 4. Zentrale Response-Erstellung
  // Egal ob aus Cache oder DB, `postsData` enthält jetzt unsere Daten.
  // Wir verwenden c.jsonT(), um die finale, typsichere Response zu erstellen.
  const response = c.json(postsData || []); // Fallback auf leeres Array, falls DB nichts findet

  // Setze die finalen Header
  response.headers.set("X-Cache-Status", cacheStatus);
  response.headers.set("Access-Control-Allow-Origin", "*");

  // Wenn es ein Cache-Miss war, setzen wir auch den Cache-Control-Header für den Browser
  if (cacheStatus === "miss") {
    response.headers.set("Cache-Control", "public, max-age=5");
  }

  return response;
};

export default feedPostsHandler;
