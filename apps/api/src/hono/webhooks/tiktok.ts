// src/hono/webhook/tiktok.ts
import { Context } from "hono";
import { AppContext } from "../../types";
import { logErrorToAnalytics } from "../../analytics-utils";

/**
 * Minimal TikTok webhook handler
 * - Supports GET for simple verification (echoes common challenge params if present)
 * - Accepts POST event payloads and logs them for diagnostics
 *
 * This is primarily intended to satisfy TikTok app review requirements.
 * We do not persist comments or other content here.
 */
export const tiktokWebhookHandler = async (c: Context<AppContext>) => {
  try {
    if (c.req.method === "POST") {
      // Read body as JSON; log basic metadata for analytics
      let payload: any = {};
      try {
        payload = await c.req.json();
      } catch {
        // Non-JSON payloads are ignored but acknowledged
        payload = { raw: await c.req.text() };
      }

      // Log to analytics for observability (fire-and-forget inside the helper)
      logErrorToAnalytics(
        c.env,
        "TIKTOK_WEBHOOK_EVENT",
        "Received TikTok webhook",
        {
          method: c.req.method,
          // Avoid type pain; log a limited subset of known headers
          headers: {
            "content-type": c.req.header("content-type"),
            "user-agent": c.req.header("user-agent"),
            "x-forwarded-for": c.req.header("x-forwarded-for"),
          },
          meta: {
            event_type: payload?.event || payload?.type || undefined,
            user_id: payload?.user_id || payload?.open_id || undefined,
          },
        }
      );

      // Return 200 OK to acknowledge receipt
      return c.text("OK", 200);
    }

    if (c.req.method !== "POST") {
      return c.text("Method Not Allowed", 405);
    }
  } catch (e: any) {
    console.error("TikTok webhook error:", e);
    logErrorToAnalytics(
      c.env,
      "TIKTOK_WEBHOOK_HANDLER_ERROR",
      "Unhandled error in TikTok webhook",
      {
        error: String(e),
      }
    );
    return c.text("Internal Server Error", 500);
  }
};
