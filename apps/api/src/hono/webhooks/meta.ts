import { z } from "zod";
import { logErrorToAnalytics } from "../../analytics-utils";
import {
  findActiveConnectionsByPlatformUserId,
  getDbClient,
  getConnectionDetails,
} from "../../database-service";
import { AppContext, WebhookSignalData } from "../../types";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq, sql } from "drizzle-orm";
import { createFactory } from "hono/factory";
import { Context } from "hono";
import { BlankInput } from "hono/types";

const InstagramMediaValueSchema = z
  .object({
    media_id: z.string(),
    comment_id: z.string().optional(),
    text: z.string().optional(),
    verb: z
      .enum(["add", "edit", "edited", "remove", "delete", "deleted"])
      .optional(),
  })
  .passthrough();
const InstagramChangeSchema = z.object({
  field: z.string(),
  value: InstagramMediaValueSchema,
});
const InstagramEntrySchema = z.object({
  id: z.string(),
  time: z.number(),
  changes: z.array(InstagramChangeSchema),
});
const InstagramWebhookPayloadSchema = z.object({
  object: z.literal("instagram"),
  entry: z.array(InstagramEntrySchema),
});

const FacebookFeedValueSchema = z
  .object({
    post_id: z.string().optional(),
    comment_id: z.string().optional(),
    from: z
      .object({
        id: z.string(),
        name: z.string(),
      })
      .optional(),
    item: z.enum(["comment", "reaction", "like", "post"]),
    verb: z.enum(["add", "edit", "edited", "remove", "delete", "deleted"]),
  })
  .passthrough();

const FacebookChangeSchema = z.object({
  field: z.string(),
  value: FacebookFeedValueSchema,
});

const FacebookEntrySchema = z.object({
  id: z.string(), // Page ID
  time: z.number(),
  changes: z.array(FacebookChangeSchema),
});

const FacebookWebhookPayloadSchema = z.object({
  object: z.literal("page"),
  entry: z.array(FacebookEntrySchema),
});

const UserDeauthValueSchema = z
  .object({ user_id: z.string().optional() })
  .passthrough();
const UserDeauthChangeSchema = z.object({
  field: z.literal("permissions"),
  value: UserDeauthValueSchema,
}); // Field ist oft 'permissions'
const UserDeauthEntrySchema = z.object({
  id: z.string(),
  time: z.number(),
  changes: z.array(UserDeauthChangeSchema),
});
const UserDeauthPayloadSchema = z.object({
  object: z.literal("user"),
  entry: z.array(UserDeauthEntrySchema),
});

const webhookHandler = async (
  c: Context<AppContext, "/webhook", BlankInput>
) => {
  let body: any;
  try {
    body = await c.req.json();
  } catch (e) {
    logErrorToAnalytics(c.env, "WEBHOOK_PARSE_ERROR", "Failed JSON parse", {
      error: String(e),
    });
    return c.text("OK", 200);
  }

  // ---- Instagram Media Updates ----
  if (body.object === "instagram") {
    const validationResult = InstagramWebhookPayloadSchema.safeParse(body);
    if (!validationResult.success) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_VALIDATION_ERROR",
        "Invalid Instagram payload",
        { errors: validationResult.error.flatten() }
      );
      return c.text("OK", 200);
    }
    const validData = validationResult.data;
    const db = getDbClient(c.env.DB);
    for (const entry of validData.entry) {
      const instagramUserId = entry.id;
      try {
        const connections = await findActiveConnectionsByPlatformUserId(
          db,
          "instagram",
          instagramUserId
        );
        if (connections.length === 0) {
          console.warn(
            `Webhook: No active connection for IG User ${instagramUserId}.`
          );
          continue;
        }
        for (const connection of connections) {
          const platformConnectionId = connection.id;
          for (const change of entry.changes) {
            const field = change.field;
            const value = change.value;
            if (
              (field === "comments" ||
                field === "reactions" ||
                field === "likes") &&
              value.media_id
            ) {
              const fullConnection = await getConnectionDetails(
                db,
                platformConnectionId
              );
              if (!fullConnection) continue;

              // Validate if the post is within the configured limit
              if (fullConnection.postLimit !== null) {
                const postCountResult = await db
                  .select({ count: sql`count(*)`.mapWith(Number) })
                  .from(schema.posts)
                  .where(
                    eq(schema.posts.platformConnectionId, platformConnectionId)
                  );
                const postCount = postCountResult[0]?.count ?? 0;

                if (postCount >= fullConnection.postLimit) {
                  console.log(
                    `Webhook for media ${value.media_id} ignored as it exceeds the post limit of ${fullConnection.postLimit}`
                  );
                  continue; // Ignore this webhook
                }
              }
              const mediaId = value.media_id;
              const doId = c.env.DEBOUNCER_DO.idFromName(platformConnectionId);
              const stub = c.env.DEBOUNCER_DO.get(doId);
              console.log(
                `Dispatching signal for media ${mediaId} (field: ${field}, verb: ${value.verb}) to DO ${platformConnectionId}`
              );
              // Signal enthält jetzt nicht mehr platformConnectionId, da diese die DO ID ist
              const signalData: Omit<
                WebhookSignalData,
                "platformConnectionId"
              > = {
                mediaId,
                field,
                verb: value.verb,
                commentId: value.comment_id,
              };
              // Erzeuge das URL-Objekt aus der Anfrage-URL
              const requestUrl = new URL(c.req.url);
              // Konstruiere die Ziel-URL für das DO Signal mit dem korrekten Hostnamen
              const signalUrl = `https://${requestUrl.hostname}/signal`;
              c.executionCtx.waitUntil(
                stub
                  .fetch(
                    new Request(signalUrl, {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify(signalData),
                    })
                  )
                  .catch((e: any) => {
                    console.error(
                      `Dispatch Error DO ${platformConnectionId}:`,
                      e
                    );
                    logErrorToAnalytics(
                      c.env,
                      "DO_DISPATCH_ERROR",
                      `Failed dispatch to DO`,
                      { platformConnectionId, mediaId, error: String(e) }
                    );
                  })
              );
            }
          }
        }
      } catch (dbError) {
        console.error("Webhook DB Error:", dbError);
        logErrorToAnalytics(
          c.env,
          "WEBHOOK_DB_LOOKUP_ERROR",
          "Webhook DB lookup failed",
          { instagramUserId, error: String(dbError) }
        );
      }
    }
  }
  // ---- Facebook Media Updates (Pages) ----
  else if (body.object === "page") {
    const validationResult = FacebookWebhookPayloadSchema.safeParse(body);
    if (!validationResult.success) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_VALIDATION_ERROR",
        "Invalid Facebook payload",
        { errors: validationResult.error.flatten(), received: body }
      );
      return c.text("OK", 200);
    }
    const validData = validationResult.data;
    const db = getDbClient(c.env.DB);
    for (const entry of validData.entry) {
      const facebookPageId = entry.id;
      try {
        const connections = await findActiveConnectionsByPlatformUserId(
          db,
          "facebook",
          facebookPageId
        );
        if (connections.length === 0) {
          console.warn(
            `Webhook: No active connection for FB Page ${facebookPageId}.`
          );
          continue;
        }

        for (const connection of connections) {
          const platformConnectionId = connection.id;
          for (const change of entry.changes) {
            const field = change.field;
            const value = change.value;

            // Handle new posts or comments
            if (field === "feed" && value.post_id) {
              const fullConnection = await getConnectionDetails(
                db,
                platformConnectionId
              );
              if (!fullConnection) continue;

              // Validate if the post is within the configured limit
              if (fullConnection.postLimit !== null) {
                const postCountResult = await db
                  .select({ count: sql`count(*)`.mapWith(Number) })
                  .from(schema.posts)
                  .where(
                    eq(schema.posts.platformConnectionId, platformConnectionId)
                  );
                const postCount = postCountResult[0]?.count ?? 0;

                if (postCount >= fullConnection.postLimit) {
                  console.log(
                    `Webhook for media ${value.post_id} ignored as it exceeds the post limit of ${fullConnection.postLimit}`
                  );
                  continue; // Ignore this webhook
                }
              }

              const mediaId = value.post_id;
              const doId = c.env.DEBOUNCER_DO.idFromName(platformConnectionId);
              const stub = c.env.DEBOUNCER_DO.get(doId);
              console.log(
                `Dispatching FB signal for media ${mediaId} (item: ${value.item}, verb: ${value.verb}) to DO ${platformConnectionId}`
              );

              const signalData: Omit<
                WebhookSignalData,
                "platformConnectionId"
              > = {
                mediaId,
                field: value.item, // 'post', 'comment', etc.
                verb: value.verb,
                commentId: value.comment_id,
              };

              const requestUrl = new URL(c.req.url);
              const signalUrl = `https://${requestUrl.hostname}/signal`;
              c.executionCtx.waitUntil(
                stub
                  .fetch(
                    new Request(signalUrl, {
                      method: "POST",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify(signalData),
                    })
                  )
                  .catch((e: any) => {
                    console.error(
                      `Dispatch Error DO ${platformConnectionId}:`,
                      e
                    );
                    logErrorToAnalytics(
                      c.env,
                      "DO_DISPATCH_ERROR",
                      `Failed dispatch to DO (Facebook)`,
                      { platformConnectionId, mediaId, error: String(e) }
                    );
                  })
              );
            }
          }
        }
      } catch (dbError) {
        console.error("Webhook DB Error (Facebook):", dbError);
        logErrorToAnalytics(
          c.env,
          "WEBHOOK_DB_LOOKUP_ERROR",
          "Webhook DB lookup failed (Facebook)",
          { facebookPageId, error: String(dbError) }
        );
      }
    }
  }
  // ---- User Deauthorization ----
  else if (body.object === "user") {
    const validationResult = UserDeauthPayloadSchema.safeParse(body);
    if (!validationResult.success) {
      logErrorToAnalytics(
        c.env,
        "WEBHOOK_VALIDATION_ERROR",
        "Invalid User Deauth payload",
        { errors: validationResult.error.flatten() }
      );
      return c.text("OK", 200);
    }
    const validData = validationResult.data;
    const db = getDbClient(c.env.DB);
    for (const entry of validData.entry) {
      // Die 'id' im entry ist hier die globale Meta User ID
      const metaUserId = entry.id;
      console.log(
        `Received deauthorization signal for platform user ${metaUserId}`
      );
      try {
        // Finde und deaktiviere alle Verbindungen, die diesem Meta User zugeordnet sind
        const result = await db
          .update(schema.platformConnections)
          .set({
            isActive: false,
            accessTokenEncrypted: null,
            refreshTokenEncrypted: null,
          }) // Token auch löschen
          .where(eq(schema.platformConnections.platformAccountId, metaUserId)) // Annahme: platformAccountId ist Meta User ID für FB/IG
          .returning({ id: schema.platformConnections.id });
        console.log(
          `Deauthorization: Marked ${result.length} connections inactive for user ${metaUserId}`
        );
        if (result.length > 0) {
          logErrorToAnalytics(
            c.env,
            "USER_DEAUTHORIZATION",
            `User deauthorized app`,
            { metaUserId, connectionsAffected: result.map((r) => r.id) }
          );
        }
      } catch (dbError) {
        console.error(`Deauth DB Error for ${metaUserId}`, dbError);
        logErrorToAnalytics(
          c.env,
          "DEAUTH_DB_ERROR",
          `Failed DB update for deauth user ${metaUserId}`,
          { metaUserId, error: String(dbError) }
        );
      }
    }
  }
  return c.text("OK", 200);
};

export default webhookHandler;
