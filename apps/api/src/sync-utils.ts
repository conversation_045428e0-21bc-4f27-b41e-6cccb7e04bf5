import { Bindings } from "./types";

export async function triggerInitialSync(
  env: Bindings,
  platformConnectionId: string
): Promise<void> {
  try {
    await env.SYNC_QUEUE.send({ platformConnectionId });
  } catch (error) {
    console.error(
      `Failed to trigger initial sync for connection ${platformConnectionId}:`,
      error
    );
    // We don't want to fail the whole OAuth flow if the initial sync trigger fails
  }
}
