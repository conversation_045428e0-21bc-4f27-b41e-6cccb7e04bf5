// src/platforms/meta.ts
import {
  ApiFetchResult,
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiComment,
  PlatformConnection,
} from "../types";
import { logErrorToAnalytics } from "../analytics-utils";
import { fetchWithRetry } from "../graph-api-shared";
import * as constants from "../constants";
import { decryptToken, encryptToken } from "../token-utils";
import { getDbClient } from "../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { DateTime } from "luxon";

const MetaTokenResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string().optional(),
  expires_in: z.number().optional(),
});

// --- Graph API Functions for Facebook/Instagram ---

export async function fetchMediaDataAndFirstComments(
  mediaId: string,
  accessToken: string,
  env: Bindings
): Promise<ApiFetchResult | null> {
  const contextInfo = `Media ${mediaId}`;
  let baseData: GraphApiMedia | undefined;
  let fetchedComments: GraphApiComment[] = [];
  let nextPageUrl: string | null = null;
  try {
    // 1. Fetch Base Media Data
    const baseFields =
      "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
    const baseUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}?fields=${baseFields}&access_token=${accessToken}`;
    const baseResponse = await fetchWithRetry(
      baseUrl,
      {},
      `${contextInfo} Base`,
      env
    );

    const baseJson = await baseResponse.json();
    const baseValidation = GraphApiMediaSchema.safeParse(baseJson);
    if (!baseValidation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Graph API media response",
        { errors: baseValidation.error.flatten(), received: baseJson }
      );
      return null;
    }
    baseData = baseValidation.data;

    // 2. Skip comment fetching - return empty comment arrays
    // Comments are not stored or used in the application
    fetchedComments = [];
    nextPageUrl = null;

    return { baseData, fetchedComments, nextPageUrl };
  } catch (error: any) {
    console.error(`Graph API: Failed to fetch ${contextInfo}:`, error);
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { mediaId, error: String(error) }
    );
    return null;
  }
}

export function checkMetaTokenFactory(platform: "facebook" | "instagram") {
  return async function (accessToken: string, env: Bindings): Promise<boolean> {
    const testUrl = `https://graph.${platform}.com/v18.0/me?fields=id&access_token=${accessToken}`;
    try {
      const response = await fetch(testUrl);
      if (!response.ok) return false;
      const jsonResp = (await response.json()) as any;
      return jsonResp.id ? true : false;
    } catch {
      return false;
    }
  };
}

export function refreshMetaTokenFactory(
  platform: "facebook" | "instagram",
  grant_type: "ig_refresh_token" | "fb_refresh_token"
) {
  return async function refreshToken(
    connection: PlatformConnection,
    env: Bindings
  ): Promise<{ accessToken: string; expiresAt: Date }> {
    if (!connection.accessTokenEncrypted) {
      throw new Error("Connection does not have an encrypted access token");
    }

    const decryptedToken = await decryptToken(
      connection.accessTokenEncrypted,
      env.ENCRYPTION_KEY
    );
    if (!decryptedToken) {
      throw new Error("Failed to decrypt access token");
    }

    const url = `https://graph.${platform}.com/refresh_access_token?grant_type=${grant_type}&access_token=${decryptedToken}`;
    const response = await fetch(url);
    const data = (await response.json()) as {
      access_token: string;
      expires_in?: number;
      error?: any;
    };

    if (data.error) {
      throw new Error(
        `Failed to refresh ${platform} token: ${data.error.message}`
      );
    }

    const newAccessToken = data.access_token;
    const newExpiresIn = data.expires_in;
    const newExpiresAt = new Date(
      Date.now() + (newExpiresIn ?? 60 * 60 * 24 * 45) * 1000
    );

    const encryptedToken = await encryptToken(
      newAccessToken,
      env.ENCRYPTION_KEY
    );
    if (!encryptedToken) {
      throw new Error("Failed to encrypt new access token");
    }

    const db = getDbClient(env.DB);
    await db
      .update(schema.platformConnections)
      .set({
        accessTokenEncrypted: encryptedToken,
        tokenExpiresAt: newExpiresAt,
        lastCheckedAt: new Date(),
        needsReconnect: false,
        hasError: false,
      })
      .where(eq(schema.platformConnections.id, connection.id));

    return { accessToken: newAccessToken, expiresAt: newExpiresAt };
  };
}

export function exchangeMetaTokenFactory(platform: "facebook" | "instagram") {
  return async function exchangeToken(
    shortLivedToken: string,
    env: Bindings
  ): Promise<{ accessToken: string; expiresAt: Date }> {
    // --- Step 1: Exchange authorization code for a short-lived access token ---
    const tokenUrl = `https://api.${platform}.com/oauth/access_token`;
    const body = new URLSearchParams();
    body.append("client_id", env.INSTAGRAM_BUSINESS_APP_ID);
    body.append("client_secret", env.INSTAGRAM_BUSINESS_APP_SECRET);
    body.append("grant_type", "authorization_code");
    body.append(
      "redirect_uri",
      `${env.NEXT_PUBLIC_API_URL}/oauth/meta/callback`
    );
    body.append("code", shortLivedToken);

    const shortLivedResponse = await fetch(tokenUrl, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body,
    });
    if (!shortLivedResponse.ok) {
      const errorText = await shortLivedResponse.text();
      console.error(
        `${platform} short-lived token exchange failed:`,
        errorText
      );
      throw new HTTPException(400, {
        message: "Short-lived token exchange failed",
      });
    }
    const shortLivedData = await shortLivedResponse.json();
    const shortLivedValidation =
      MetaTokenResponseSchema.safeParse(shortLivedData);

    if (!shortLivedValidation.success) {
      console.error(
        "Invalid short-lived token response:",
        shortLivedValidation.error
      );
      throw new HTTPException(400, {
        message: "Invalid short-lived token response",
      });
    }
    const shortLivedAccessToken = shortLivedValidation.data.access_token;

    // --- Step 2: Exchange the short-lived token for a long-lived token ---
    const longLivedUrl = `https://graph.${platform}.com/access_token`;
    const longLivedBody = new URLSearchParams();
    longLivedBody.append("grant_type", "ig_exchange_token");
    longLivedBody.append("client_secret", env.INSTAGRAM_BUSINESS_APP_SECRET);
    longLivedBody.append("access_token", shortLivedAccessToken);

    const longLivedResponse = await fetch(longLivedUrl, {
      method: "GET",
      // Note: For long-lived token exchange, parameters are sent in the query string for GET requests.
      // However, the Graph API for this specific exchange might be expecting a POST.
      // Let's stick with the documented GET method for now.
    });

    const finalUrl = `${longLivedUrl}?${longLivedBody.toString()}`;

    const finalResponse = await fetch(finalUrl);

    if (!finalResponse.ok) {
      const errorText = await finalResponse.text();
      console.error(`${platform} long-lived token exchange failed:`, errorText);
      throw new HTTPException(400, {
        message: "Long-lived token exchange failed",
      });
    }

    const longLivedData = await finalResponse.json();
    const longLivedValidation = MetaTokenResponseSchema.extend({
      expires_in: z.number(),
    }).safeParse(longLivedData);

    if (!longLivedValidation.success) {
      console.error(
        "Invalid long-lived token response:",
        longLivedValidation.error
      );
      throw new HTTPException(400, {
        message: "Invalid long-lived token response",
      });
    }

    return {
      accessToken: longLivedValidation.data.access_token,
      expiresAt: DateTime.now()
        .plus({
          seconds: longLivedValidation.data.expires_in,
        })
        .toJSDate(),
    };
  };
}
