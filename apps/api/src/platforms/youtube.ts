// src/platforms/youtube.ts
import { z } from "zod";
import {
  Bindings,
  InsertPost,
  PlatformConnection,
  Post,
  YouTubeChannelResponseSchema,
  YouTubeVideosResponseSchema,
} from "../types";
import { logErrorToAnalytics } from "../analytics-utils";
import { fetchWithRetry, GraphApiError } from "../graph-api-shared";
import { decryptToken, encryptToken } from "../token-utils";
import { getDbClient } from "../database-service";
import { platformConnections } from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";
import { DateTime } from "luxon";
import { HTTPException } from "hono/http-exception";
import { PlatformInformation } from ".";

/**
 * Shared fetch function with retry logic for all platforms
 */
export async function fetchWithRetryAndRefresh(
  url: string,
  options: RequestInit = {},
  contextInfo: string,
  env: Bindings,
  connection: PlatformConnection
): Promise<Response> {
  try {
    const ret = await fetchWithRetry(url, options, contextInfo, env);
    return ret;
  } catch (e) {
    if (e instanceof GraphApiError && e.isAuthError) {
      console.warn(`Auth error for ${contextInfo}, refreshing token...`);
      // Attempt to refresh the token
      const newTokenData = await refreshYoutubeToken(connection, env);
      connection.accessTokenEncrypted = await encryptToken(
        newTokenData.accessToken,
        env.ENCRYPTION_KEY
      );
      connection.tokenExpiresAt = newTokenData.expiresAt;

      // Retry the original request with the new token
      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${newTokenData.accessToken}`,
      };
      return fetchWithRetry(url, options, contextInfo, env);
    }
    throw e; // Re-throw if not an auth error
  }
}

export async function getYoutubePlatformInformation(
  connection: PlatformConnection,
  env: Bindings
): Promise<PlatformInformation> {
  if (!connection.platformAccountId) {
    throw new Error("Missing platformAccountId in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted!,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt YouTube access token");
  }
  const refreshToken = connection.refreshTokenEncrypted
    ? await decryptToken(connection.refreshTokenEncrypted, env.ENCRYPTION_KEY)
    : undefined;

  const contextInfo = `YouTube Channel ${connection.platformAccountId}`;
  const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics,brandingSettings&id=${connection.platformAccountId}`;

  const response = await fetchWithRetryAndRefresh(
    url,
    {
      method: "GET",
      headers: {
        // Hier wird der Access Token im Header übergeben
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    },
    `${contextInfo} Info`,
    env,
    connection
  );

  const jsonResp = await response.json();
  console.log("YouTube channel info response:", jsonResp);
  const validation = YouTubeChannelResponseSchema.safeParse(jsonResp);

  if (!validation.success) {
    logErrorToAnalytics(
      env,
      "YOUTUBE_CHANNEL_VALIDATION_ERROR",
      "Invalid YouTube channel response",
      { errors: validation.error.flatten(), received: jsonResp }
    );
    throw new Error(
      `YouTube channel validation failed: ${validation.error.message}`
    );
  }

  if (validation.data.items.length === 0) {
    console.warn(
      `YouTube: No channel found for ID ${connection.platformAccountId}`
    );
    throw new Error(`No channel found for ID ${connection.platformAccountId}`);
  }

  const channel = validation.data.items[0];

  return {
    id: channel.id,
    name: channel.snippet.title,
    description: channel.snippet.description,
    followers_count:
      parseInt(channel.statistics?.subscriberCount ?? "0", 10) || 0,
    posts_count: parseInt(channel.statistics?.videoCount ?? "0", 10) || 0,
    profile_picture_url: channel.snippet.thumbnails?.default?.url,
    views_count: parseInt(channel.statistics?.viewCount ?? "0", 10) || 0,
  };
}

/**
 * Checks if a YouTube access token is valid
 */
export async function checkYoutubeToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://www.googleapis.com/youtube/v3/channels?part=id&mine=true&access_token=${accessToken}`;
  try {
    console.log("YouTube: Checking token validity...");
    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `YouTube token check failed: ${response.status} ${response.statusText}`,
        errorText
      );

      // Log specific error for analytics
      logErrorToAnalytics(
        env,
        "YOUTUBE_TOKEN_CHECK_FAILED",
        `YouTube token validation failed: ${response.status}`,
        {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
        }
      );
      return false;
    }

    const jsonResp = (await response.json()) as any;
    console.log("YouTube token check response:", jsonResp);

    const isValid = jsonResp.items && jsonResp.items.length > 0;
    console.log(`YouTube token is ${isValid ? "valid" : "invalid"}`);

    return isValid;
  } catch (e) {
    console.error("YouTube token check failed with exception:", e);
    logErrorToAnalytics(
      env,
      "YOUTUBE_TOKEN_CHECK_ERROR",
      "YouTube token check threw exception",
      { error: String(e) }
    );
    return false;
  }
}

export async function refreshYoutubeToken(
  connection: PlatformConnection,
  env: Bindings
): Promise<{ accessToken: string; expiresAt: Date }> {
  if (!connection.refreshTokenEncrypted) {
    throw new Error("No refresh token available for YouTube connection");
  }

  const refreshToken = await decryptToken(
    connection.refreshTokenEncrypted,
    env.ENCRYPTION_KEY
  );

  if (!refreshToken) {
    throw new Error("Failed to decrypt YouTube refresh token");
  }

  const tokenUrl = "https://oauth2.googleapis.com/token";
  const params = new URLSearchParams();
  params.append("client_id", env.GOOGLE_CLIENT_ID);
  params.append("client_secret", env.GOOGLE_CLIENT_SECRET);
  params.append("refresh_token", refreshToken);
  params.append("grant_type", "refresh_token");

  const response = await fetch(tokenUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: params.toString(),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("YouTube token refresh failed:", errorText);
    throw new Error(`YouTube token refresh failed: ${errorText}`);
  }

  const tokenData = await response.json<{
    access_token: string;
    expires_in: number;
  }>();
  console.log("YouTube token refresh response:", tokenData);

  if (!tokenData.access_token || !tokenData.expires_in) {
    throw new Error("Invalid YouTube token refresh response");
  }

  const expires_at = DateTime.now().plus({ seconds: tokenData.expires_in });

  const encryptedToken = await encryptToken(
    tokenData.access_token,
    env.ENCRYPTION_KEY
  );

  const db = getDbClient(env.DB);
  await db
    .update(platformConnections)
    .set({
      accessTokenEncrypted: encryptedToken,
      tokenExpiresAt: expires_at.toJSDate(),
    })
    .where(eq(platformConnections.id, connection.id));

  return {
    accessToken: tokenData.access_token,
    expiresAt: expires_at.toJSDate(),
  };
}

export async function exchangeYoutubeToken(
  shortLivedToken: string,
  env: Bindings
): Promise<{ accessToken: string; refreshToken?: string; expiresAt: Date }> {
  try {
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        code: shortLivedToken,
        client_id: env.GOOGLE_CLIENT_ID,
        client_secret: env.GOOGLE_CLIENT_SECRET,
        redirect_uri: `${env.NEXT_PUBLIC_API_URL}/oauth/youtube/callback`,
        grant_type: "authorization_code",
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error("YouTube token exchange failed:", errorText);
      throw new HTTPException(400, { message: "Token exchange failed" });
    }

    const tokenData = await tokenResponse.json();

    const YouTubeTokenResponseSchema = z.object({
      access_token: z.string(),
      refresh_token: z.string().optional(),
      expires_in: z.number(),
      scope: z.string(),
      token_type: z.string(),
    });

    const validation = YouTubeTokenResponseSchema.safeParse(tokenData);

    if (!validation.success) {
      console.error("Invalid token response:", validation.error);
      throw new HTTPException(400, { message: "Invalid token response" });
    }

    const { access_token, refresh_token, expires_in, scope } = validation.data;

    const expires_at = DateTime.now().plus({ seconds: expires_in });

    return {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiresAt: expires_at.toJSDate(),
    };
  } catch (error) {
    console.error("YouTube token exchange error:", error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_TOKEN_EXCHANGE_ERROR",
      "Failed to exchange YouTube token",
      { error: String(error) }
    );
    throw error;
  }
}

export function mapYoutubePostData(
  post: any,
  connection: PlatformConnection
): InsertPost {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "youtube",
    likeCount: parseInt(post.statistics?.likeCount || "0", 10),
    commentsCount: parseInt(post.statistics?.commentCount || "0", 10),
    caption: post.snippet?.title || "",
    mediaUrl: `https://www.youtube.com/watch?v=${post.id}`,
    mediaType: "VIDEO",
    permalink: `https://www.youtube.com/watch?v=${post.id}`,
    timestamp: new Date(post.snippet?.publishedAt),
    lastFetched: new Date(),
  };
}

export async function getYoutubePost(
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<Post | null> {
  if (!connection.accessTokenEncrypted) {
    throw new Error("No access token available for YouTube connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );

  if (!accessToken) {
    throw new Error("Failed to decrypt YouTube access token");
  }

  const detailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${postId}`;
  const detailsResponse = await fetchWithRetryAndRefresh(
    detailsUrl,
    {
      method: "GET",
      headers: {
        // Hier wird der Access Token im Header übergeben
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    },
    `YouTube Post ${postId} Details`,
    env,
    connection
  );
  const detailsJson = await detailsResponse.json();
  const validation = YouTubeVideosResponseSchema.safeParse(detailsJson);

  if (!validation.success || validation.data.items.length === 0) {
    return null;
  }

  return mapYoutubePostData(validation.data.items[0], connection) as Post;
}

export async function getYoutubePosts(
  connection: PlatformConnection,
  args: { limit?: number; cursor?: string },
  env: Bindings
): Promise<{ posts: Post[]; nextCursor?: string }> {
  if (!connection.accessTokenEncrypted) {
    throw new Error("No access token available for YouTube connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt YouTube access token");
  }

  const channelId = connection.platformAccountId;
  if (!channelId) {
    throw new Error("Missing platformAccountId in connection");
  }

  const limit = args.limit ?? connection.postLimit ?? 50;
  const contextInfo = `YouTube Videos for ${channelId}`;
  const collectedVideos: any[] = [];
  let pageToken: string | undefined = args.cursor;

  while (collectedVideos.length < limit) {
    const remainingLimit = limit - collectedVideos.length;
    const maxResults = Math.min(remainingLimit, 50); // YouTube API max per request is 50

    let searchUrl = `https://www.googleapis.com/youtube/v3/search?part=id&channelId=${channelId}&type=video&order=date&maxResults=${maxResults}`;
    if (pageToken) {
      searchUrl += `&pageToken=${pageToken}`;
    }

    const searchResponse = await fetchWithRetryAndRefresh(
      searchUrl,
      {
        method: "GET",
        headers: {
          // Hier wird der Access Token im Header übergeben
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
      },
      `${contextInfo} Search`,
      env,
      connection
    );
    const searchJson = (await searchResponse.json()) as any;

    if (!searchJson.items || searchJson.items.length === 0) {
      break; // No more videos found
    }

    const videoIds = searchJson.items
      .map((item: any) => item.id.videoId)
      .filter(Boolean);

    if (videoIds.length > 0) {
      const detailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${videoIds.join(
        ","
      )}`;
      const detailsResponse = await fetchWithRetryAndRefresh(
        detailsUrl,
        {
          method: "GET",
          headers: {
            // Hier wird der Access Token im Header übergeben
            Authorization: `Bearer ${accessToken}`,
            Accept: "application/json",
          },
        },
        `${contextInfo} Details`,
        env,
        connection
      );
      const detailsJson = await detailsResponse.json();
      const validation = YouTubeVideosResponseSchema.safeParse(detailsJson);

      if (validation.success) {
        collectedVideos.push(...validation.data.items);
      } else {
        logErrorToAnalytics(
          env,
          "YOUTUBE_VIDEOS_VALIDATION_ERROR",
          "Invalid YouTube videos response",
          { errors: validation.error.flatten(), received: detailsJson }
        );
      }
    }

    pageToken = searchJson.nextPageToken;
    if (!pageToken) {
      break; // No more pages
    }
  }

  const videos = collectedVideos.slice(0, limit);
  const nextCursor = pageToken;

  const posts = videos.map((video) => mapYoutubePostData(video, connection));
  return { posts: posts as Post[], nextCursor };
}
