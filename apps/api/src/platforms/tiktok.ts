// src/platforms/tiktok.ts
import { Bindings, InsertPost, PlatformConnection } from "../types";
import { fetchWithRetry, GraphApiError } from "../graph-api-shared";

export async function checkTikTokToken(accessToken: string, env: Bindings) {
  try {
    const resp = await fetchWithRetry(
      "https://open.tiktokapis.com/v2/user/info/",
      { headers: { Authorization: `Bearer ${accessToken}` } },
      "TikTok Token Check",
      env
    );
    const json = await resp.json<any>();
    return Boolean(json?.data?.user?.open_id);
  } catch {
    return false;
  }
}

export async function fetchTikTokVideos(
  openId: string,
  accessToken: string,
  env: Bindings,
  limit: number = 50
) {
  const contextInfo = `TikTok Videos for ${openId}`;
  const url = new URL("https://open.tiktokapis.com/v2/video/list/");
  url.searchParams.set(
    "fields",
    "id,create_time,cover_image_url,share_url,video_description,duration,height,width,title,embed_html,embed_link,like_count,comment_count,share_count,view_count"
  );
  url.searchParams.set("max_count", String(Math.min(limit, 20))); // Max 20 per page for this endpoint

  try {
    const resp = await fetchWithRetry(
      url.toString(),
      { headers: { Authorization: `Bearer ${accessToken}` } },
      contextInfo,
      env
    );

    const json = await resp.json<any>();
    if (json?.error?.code) {
      const { code, message } = json.error;
      const isAuthError = [
        "access_token_invalid",
        "invalid_scope",
        "authorization_code_invalid",
      ].includes(code);
      throw new GraphApiError(
        message,
        resp.status,
        !isAuthError, // Don't retry auth errors
        isAuthError
      );
    }

    const items = json?.data?.videos ?? [];
    return { posts: items };
  } catch (error) {
    console.error(`${contextInfo}: Failed to fetch videos:`, error);
    return { posts: [], error };
  }
}

export function mapTikTokPost(
  post: any,
  connection: PlatformConnection
): InsertPost {
  const stats = post.stats || post.statistics || {};
  return {
    platformConnectionId: connection.id,
    mediaId: String(post.id || post.video_id),
    platform: "tiktok",
    likeCount: Number(stats?.like_count ?? 0),
    commentsCount: Number(stats?.comment_count ?? 0),
    caption: post?.title || post?.desc || null,
    mediaUrl: post?.cover_image_url || post?.thumbnail_url || null,
    mediaType: "video",
    permalink: post?.share_url || post?.permalink || null,
    timestamp: post?.create_time
      ? new Date(Number(post.create_time) * 1000)
      : new Date(),
    lastFetched: new Date(),
    lastWebhookUpdate: null,
  };
}
