import { z } from "zod";
import {
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiPagingSchema,
  InsertPost,
  PlatformConnection,
  Post,
} from "../types";
import * as constants from "../constants";
import { HTTPException } from "hono/http-exception";
import { decryptToken, encryptToken } from "../token-utils";
import { PlatformInformation } from ".";
import { DateTime } from "luxon";
import { getDbClient } from "../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

const InstagramTokenResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string().optional(),
  expires_in: z.number().optional(),
});

const InstagramBusinessUserResponseSchema = z.object({
  id: z.string(),
  username: z.string(),
  name: z.string().optional(),
  biography: z.string().optional(),
  followers_count: z.number().optional(),
  follows_count: z.number().optional(),
  media_count: z.number().optional(),
  profile_picture_url: z.string().url().optional(),
});

export async function checkInstagramToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const testUrl = `https://graph.instagram.com/v18.0/me?fields=id&access_token=${accessToken}`;
  try {
    const response = await fetch(testUrl);
    if (!response.ok) return false;
    const jsonResp = (await response.json()) as any;
    return jsonResp.id ? true : false;
  } catch {
    return false;
  }
}

export async function refreshInstagramToken(
  connection: PlatformConnection,
  env: Bindings
): Promise<{ accessToken: string; expiresAt: Date }> {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Connection does not have an encrypted access token");
  }

  const decryptedToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!decryptedToken) {
    throw new Error("Failed to decrypt access token");
  }

  const url = `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${decryptedToken}`;
  const response = await fetch(url);
  const data = (await response.json()) as {
    access_token: string;
    expires_in?: number;
    error?: any;
  };

  if (data.error) {
    throw new Error(`Failed to refresh Instagram token: ${data.error.message}`);
  }

  const newAccessToken = data.access_token;
  const newExpiresIn = data.expires_in;
  const newExpiresAt = new Date(
    Date.now() + (newExpiresIn ?? 60 * 60 * 24 * 45) * 1000
  );

  const encryptedToken = await encryptToken(newAccessToken, env.ENCRYPTION_KEY);
  if (!encryptedToken) {
    throw new Error("Failed to encrypt new access token");
  }

  const db = getDbClient(env.DB);
  await db
    .update(schema.platformConnections)
    .set({
      accessTokenEncrypted: encryptedToken,
      tokenExpiresAt: newExpiresAt,
      lastCheckedAt: new Date(),
      needsReconnect: false,
      hasError: false,
    })
    .where(eq(schema.platformConnections.id, connection.id));

  return { accessToken: newAccessToken, expiresAt: newExpiresAt };
}

export async function exchangeInstagramToken(
  shortLivedToken: string,
  env: Bindings
): Promise<{ accessToken: string; expiresAt: Date }> {
  // --- Step 1: Exchange authorization code for a short-lived access token ---
  const tokenUrl = `https://api.instagram.com/oauth/access_token`;
  const body = new URLSearchParams();
  body.append("client_id", env.INSTAGRAM_BUSINESS_APP_ID);
  body.append("client_secret", env.INSTAGRAM_BUSINESS_APP_SECRET);
  body.append("grant_type", "authorization_code");
  body.append("redirect_uri", `${env.NEXT_PUBLIC_API_URL}/oauth/meta/callback`);
  body.append("code", shortLivedToken);

  const shortLivedResponse = await fetch(tokenUrl, {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body,
  });
  if (!shortLivedResponse.ok) {
    const errorText = await shortLivedResponse.text();
    console.error(`Instagram short-lived token exchange failed:`, errorText);
    throw new HTTPException(400, {
      message: "Short-lived token exchange failed",
    });
  }
  const shortLivedData = await shortLivedResponse.json();
  const shortLivedValidation =
    InstagramTokenResponseSchema.safeParse(shortLivedData);

  if (!shortLivedValidation.success) {
    console.error(
      "Invalid short-lived token response:",
      shortLivedValidation.error
    );
    throw new HTTPException(400, {
      message: "Invalid short-lived token response",
    });
  }
  const shortLivedAccessToken = shortLivedValidation.data.access_token;

  // --- Step 2: Exchange the short-lived token for a long-lived token ---
  const longLivedUrl = `https://graph.instagram.com/access_token`;
  const longLivedBody = new URLSearchParams();
  longLivedBody.append("grant_type", "ig_exchange_token");
  longLivedBody.append("client_id", env.INSTAGRAM_BUSINESS_APP_ID);
  longLivedBody.append("client_secret", env.INSTAGRAM_BUSINESS_APP_SECRET);
  longLivedBody.append("access_token", shortLivedAccessToken);

  const finalResponse = await fetch(
    `${longLivedUrl}?${longLivedBody.toString()}`
  );

  if (!finalResponse.ok) {
    const errorText = await finalResponse.text();
    console.error(`Instagram long-lived token exchange failed:`, errorText);
    throw new HTTPException(400, {
      message: "Long-lived token exchange failed",
    });
  }

  const longLivedData = await finalResponse.json();
  const longLivedValidation =
    InstagramTokenResponseSchema.safeParse(longLivedData);

  if (!longLivedValidation.success) {
    console.error(
      "Invalid long-lived token response:",
      longLivedValidation.error
    );
    throw new HTTPException(400, {
      message: "Invalid long-lived token response",
    });
  }

  return {
    accessToken: longLivedValidation.data.access_token,
    expiresAt: DateTime.now()
      .plus({
        seconds: longLivedValidation.data.expires_in,
      })
      .toJSDate(),
  };
}

export const getPlatformInformation = async (
  connection: PlatformConnection,
  env: Bindings
): Promise<PlatformInformation> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,username,name,biography,followers_count,follows_count,profile_picture_url,media_count";
  const userResponse = await fetch(
    `https://graph.instagram.com/v19.0/me?fields=${fields}&access_token=${accessToken}`
  );

  if (!userResponse.ok) {
    const errorText = await userResponse.text();
    console.error("Failed to get user info from graph", errorText);
    throw new HTTPException(400, { message: "Failed to get user info" });
  }

  const userData = await userResponse.json();
  const userValidation =
    InstagramBusinessUserResponseSchema.safeParse(userData);

  if (!userValidation.success) {
    console.error("Invalid user response:", userValidation.error);
    throw new HTTPException(400, { message: "Invalid user response" });
  }

  const data = userValidation.data;
  return {
    id: data.id,
    name: data.name || data.username,
    username: data.username,
    description: data.biography,
    followers_count: data.followers_count,
    follows_count: data.follows_count,
    posts_count: data.media_count,
    profile_picture_url: data.profile_picture_url,
  };
};

export const getPost = async (
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<any | null> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const url = `https://graph.instagram.com/${constants.META_API_VERSION}/${postId}?fields=${fields}&access_token=${accessToken}`;
  const response = await fetch(url);
  if (!response.ok) {
    return null;
  }
  const postData = await response.json();
  const postValidation = GraphApiMediaSchema.safeParse(postData);

  if (!postValidation.success) {
    return null;
  }

  return postValidation.data;
};

export const getPosts = async (
  connection: PlatformConnection,
  { limit, cursor }: { limit?: number; cursor?: string },
  env: Bindings
): Promise<{ posts: any[]; nextCursor?: string }> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url = `https://graph.instagram.com/${constants.META_API_VERSION}/${connection.platformAccountId}/media?fields=${fields}&limit=${limit ?? 10}&access_token=${accessToken}`;

  if (cursor) {
    url += `&after=${cursor}`;
  }

  const response = await fetch(url);
  if (!response.ok) {
    return { posts: [] };
  }
  const pageJson = await response.json();
  const pageValidation = z
    .object({
      data: z.array(GraphApiMediaSchema),
      paging: GraphApiPagingSchema.optional(),
    })
    .safeParse(pageJson);

  if (!pageValidation.success) {
    return { posts: [] };
  }

  return {
    posts: pageValidation.data.data, // Return raw Instagram posts
    nextCursor: pageValidation.data.paging?.cursors?.after,
  };
};

export function mapInstagramBusinessPostData(
  post: GraphApiMedia,
  connection: PlatformConnection
): InsertPost {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "instagram",
    likeCount: post.like_count ?? 0,
    commentsCount: post.comments_count ?? 0,
    caption: post.caption ?? null,
    mediaUrl: post.media_url ?? null,
    mediaType: post.media_type ?? "UNKNOWN",
    permalink: post.permalink ?? null,
    timestamp: new Date(post.timestamp),
    lastFetched: new Date(),
  };
}
