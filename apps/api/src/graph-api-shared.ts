// src/graph-api-shared.ts - Shared API utilities
import { z } from "zod";
import {
  ApiFetchResult,
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiCommentsResponseSchema,
  GraphApiComment,
  GraphApiMeSchema,
} from "./types";
import { logErrorToAnalytics } from "./analytics-utils";
import * as constants from "./constants";

export class GraphApiError extends Error {
  status?: number;
  isRetryable: boolean;
  isAuthError: boolean;

  constructor(
    message: string,
    status?: number,
    isRetryable: boolean = true,
    isAuthError: boolean = false
  ) {
    super(message);
    this.name = "GraphApiError";
    this.status = status;
    this.isRetryable = isRetryable;
    this.isAuthError = isAuthError;
  }
}

/**
 * Shared fetch function with retry logic for all platforms
 */
export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  contextInfo: string,
  env: Bindings
): Promise<Response> {
  let attempts = 0;
  let delay = constants.INITIAL_RETRY_DELAY_MS;

  while (attempts < constants.MAX_API_RETRIES) {
    attempts++;
    try {
      const response = await fetch(url, options);
      if (response.ok) return response;

      const status = response.status;
      // Versuche Body zu lesen für Fehlerdetails
      let errorBodyText: string | null = null;
      try {
        errorBodyText = await response.clone().text();
      } catch {
        /* ignore */
      }

      console.error(
        `API: Error (Attempt ${attempts}, Status ${status}) for ${contextInfo}:`,
        errorBodyText ?? "(no body)"
      );

      if (status >= 400 && status < 500 && status !== 429) {
        const isAuth =
          [401, 403].includes(status) ||
          (status === 400 && errorBodyText?.includes("OAuthException"));
        throw new GraphApiError(
          `Non-retryable API error ${status}`,
          status,
          false,
          isAuth
        );
      }

      if (status === 429 || status >= 500) {
        if (attempts >= constants.MAX_API_RETRIES) {
          throw new GraphApiError(
            `Max retries exceeded for ${contextInfo}`,
            status,
            false
          );
        }
        console.log(
          `API: Retrying ${contextInfo} in ${delay}ms (attempt ${attempts})`
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
        delay = Math.min(delay * 2, constants.MAX_RETRY_DELAY_MS);
        continue;
      }

      throw new GraphApiError(`Unexpected status ${status}`, status, false);
    } catch (error) {
      if (error instanceof GraphApiError) throw error;
      if (attempts >= constants.MAX_API_RETRIES) {
        throw new GraphApiError(
          `Network error after ${attempts} attempts: ${String(error)}`,
          undefined,
          false
        );
      }
      console.log(
        `API: Network error, retrying ${contextInfo} in ${delay}ms (attempt ${attempts})`
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
      delay = Math.min(delay * 2, constants.MAX_RETRY_DELAY_MS);
    }
  }

  throw new GraphApiError(
    `Max retries exceeded for ${contextInfo}`,
    undefined,
    false
  );
}

/**
 * Legacy function for webhook/debouncer compatibility
 * Consider migrating to platform-specific adapters
 */
export async function fetchMediaDataAndFirstComments(
  mediaId: string,
  accessToken: string,
  env: Bindings
): Promise<ApiFetchResult | null> {
  const contextInfo = `Media ${mediaId}`;
  let baseData: GraphApiMedia | undefined;
  let fetchedComments: GraphApiComment[] = [];
  let nextPageUrl: string | null = null;
  try {
    // 1. Fetch Base Media Data
    const baseFields =
      "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
    const baseUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}?fields=${baseFields}&access_token=${accessToken}`;
    const baseResponse = await fetchWithRetry(
      baseUrl,
      {},
      `${contextInfo} Base`,
      env
    );
    const baseJson = await baseResponse.json();
    const baseValidation = GraphApiMediaSchema.safeParse(baseJson);
    if (!baseValidation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        `Invalid base media data structure for ${mediaId}`,
        {
          context: contextInfo,
          errors: baseValidation.error.flatten(),
          received: baseJson,
        }
      );
      throw new GraphApiError(
        "Invalid base API response structure",
        baseResponse.status,
        false
      );
    }
    baseData = baseValidation.data;

    // 2. Skip comment fetching - return empty comment arrays
    // Comments are not stored or used in the application
    fetchedComments = [];
    nextPageUrl = null;

    return { baseData, fetchedComments, nextPageUrl };
  } catch (error: any) {
    console.error(`API: Failed to fetch ${contextInfo}:`, error);
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching media",
      { mediaId, error: String(error) }
    );
    return null;
  }
}

/**
 * Legacy function for pagination queue compatibility
 */
export async function fetchNextCommentPage(
  nextPageUrl: string,
  env: Bindings
): Promise<{ comments: GraphApiComment[]; nextPageUrl: string | null } | null> {
  // Skip comment fetching - return empty comment arrays
  // Comments are not stored or used in the application
  return {
    comments: [],
    nextPageUrl: null,
  };
}
