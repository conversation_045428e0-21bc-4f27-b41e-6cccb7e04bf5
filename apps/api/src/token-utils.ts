// src/token-utils.ts
import { xchacha20poly1305 } from "@noble/ciphers/chacha";
import { randomBytes } from "@noble/ciphers/webcrypto";
import { sha256 } from "@noble/hashes/sha256";

/**
 * Simple and reliable token encryption using @noble/ciphers
 * Uses XChaCha20-Poly1305 which is secure, fast, and simple
 */

// Convert string to Uint8Array
function stringToBytes(str: string): Uint8Array {
  return new TextEncoder().encode(str);
}

// Convert Uint8Array to string
function bytesToString(bytes: Uint8Array): string {
  return new TextDecoder().decode(bytes);
}

// Convert Uint8Array to base64
function bytesToBase64(bytes: Uint8Array): string {
  return btoa(String.fromCharCode(...bytes));
}

// Convert base64 to Uint8Array
function base64ToBytes(base64: string): Uint8Array {
  return Uint8Array.from(atob(base64), (c) => c.charCodeAt(0));
}

// Derive a 32-byte key from password using SHA-256
function deriveKey(password: string, salt: Uint8Array): Uint8Array {
  const passwordBytes = stringToBytes(password);
  const combined = new Uint8Array(passwordBytes.length + salt.length);
  combined.set(passwordBytes, 0);
  combined.set(salt, passwordBytes.length);
  return sha256(combined);
}

/**
 * Encrypts a token using XChaCha20-Poly1305
 * Returns base64 encoded string containing salt + nonce + encrypted data
 */
export async function encryptToken(
  token: string,
  encryptionKey: string
): Promise<string | null> {
  if (!token || !encryptionKey) {
    console.error("Token or encryption key is missing");
    return null;
  }

  try {
    // Generate random salt and nonce
    const salt = randomBytes(16);
    const nonce = randomBytes(24); // 24-byte nonce for XChaCha20

    // Derive key from password and salt
    const key = deriveKey(encryptionKey, salt);

    // Create cipher
    const cipher = xchacha20poly1305(key, nonce);

    // Encrypt the token
    const tokenBytes = stringToBytes(token);
    const encryptedData = cipher.encrypt(tokenBytes);

    // Combine salt + nonce + encrypted data
    const combined = new Uint8Array(
      salt.length + nonce.length + encryptedData.length
    );
    combined.set(salt, 0);
    combined.set(nonce, salt.length);
    combined.set(encryptedData, salt.length + nonce.length);

    // Return as base64
    return bytesToBase64(combined);
  } catch (error) {
    console.error("Token encryption failed:", error);
    return null;
  }
}

/**
 * Decrypts a token that was encrypted with encryptToken
 * Expects base64 encoded string containing salt + nonce + encrypted data
 */
export async function decryptToken(
  encryptedTokenB64: string,
  encryptionKey: string
): Promise<string | null> {
  if (!encryptedTokenB64 || !encryptionKey) {
    console.error("Encrypted token or encryption key is missing");
    return null;
  }

  try {
    // Decode from base64
    const combined = base64ToBytes(encryptedTokenB64);

    // Extract salt, nonce, and encrypted data
    const salt = combined.slice(0, 16);
    const nonce = combined.slice(16, 40); // 24 bytes for XChaCha20 nonce
    const encryptedData = combined.slice(40);

    // Derive the same key
    const key = deriveKey(encryptionKey, salt);

    // Create cipher with the same nonce
    const cipher = xchacha20poly1305(key, nonce);

    // Decrypt
    const decryptedData = cipher.decrypt(encryptedData);

    return bytesToString(decryptedData);
  } catch (error) {
    console.error("Token decryption failed:", error);
    return null;
  }
}
