import { getDbClient } from "../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { and, isNotNull, lte, ne } from "drizzle-orm";
import { getPlatformAdapter } from "../platforms";
import { Bindings } from "../types";

export default async function handler(env: Bindings) {
  const db = getDbClient(env.DB);
  const oneDayAgo = new Date(Date.now() - 1000 * 60 * 60 * 24);
  const fourteenDaysFromNow = new Date(Date.now() + 1000 * 60 * 60 * 24 * 14);

  const connectionsToRefresh = await db
    .select()
    .from(schema.platformConnections)
    .where(
      and(
        ne(schema.platformConnections.platform, "youtube"),
        isNotNull(schema.platformConnections.tokenExpiresAt),
        lte(schema.platformConnections.tokenExpiresAt, fourteenDaysFromNow),
        lte(schema.platformConnections.lastCheckedAt, oneDayAgo)
      )
    );

  for (const connection of connectionsToRefresh) {
    const platformAdapter = getPlatformAdapter(connection.platform);
    if (platformAdapter.refreshToken) {
      await platformAdapter.refreshToken(connection, env);
    }
  }
}
