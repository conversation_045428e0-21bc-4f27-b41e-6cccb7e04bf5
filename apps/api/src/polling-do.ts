import { drizzle } from "drizzle-orm/d1";
import { platformConnections } from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

export interface Env {
  DB: D1Database;
}

const RATE_LIMIT = 600;
const RATE_LIMIT_WINDOW = 60 * 1000; // 60 seconds

/**
 * AccountPollingDO is a Durable Object that ensures only one polling operation
 * occurs at a time for a specific TikTok account.
 */
export class AccountPollingDO {
  state: DurableObjectState;
  env: Env;
  db: ReturnType<typeof drizzle>;

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    this.db = drizzle(env.DB);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    if (path === "/lock") {
      return this.handleLock(request);
    } else if (path === "/release") {
      return this.handleRelease(request);
    }

    return new Response("Not found", { status: 404 });
  }

  /**
   * Attempts to acquire a lock for the account.
   */
  async handleLock(request: Request): Promise<Response> {
    const { platformConnectionId } = await request.json<{
      platformConnectionId: string;
    }>();

    const lockedAt: number | null =
      (await this.state.storage.get("lockedAt")) ?? null;

    // Lock is considered stale after 5 minutes
    const staleTime = 5 * 60 * 1000;

    if (lockedAt && Date.now() - lockedAt < staleTime) {
      return new Response("Account is already being polled.", { status: 409 }); // Conflict
    }

    const now = Date.now();
    await this.state.storage.put("lockedAt", now);

    // Also update the database to reflect the lock
    await this.db
      .update(platformConnections)
      .set({ lockedAt: new Date(now) })
      .where(eq(platformConnections.id, platformConnectionId));

    return new Response(JSON.stringify({ success: true, lockedAt: now }), {
      headers: { "Content-Type": "application/json" },
    });
  }

  /**
   * Releases the lock for the account.
   */
  async handleRelease(request: Request): Promise<Response> {
    const { platformConnectionId } = await request.json<{
      platformConnectionId: string;
    }>();

    await this.state.storage.delete("lockedAt");

    // Also update the database to remove the lock
    await this.db
      .update(platformConnections)
      .set({ lockedAt: null })
      .where(eq(platformConnections.id, platformConnectionId));

    return new Response(JSON.stringify({ success: true }), {
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * RateLimiterDO is a Durable Object that enforces a rate limit on outgoing
 * requests to the TikTok API.
 */
export class RateLimiterDO {
  state: DurableObjectState;
  cooldownUntil: number | null = null;

  constructor(state: DurableObjectState) {
    this.state = state;
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    if (path === "/check") {
      return this.handleCheck();
    } else if (path === "/initiateBackoff") {
      return this.handleInitiateBackoff(request);
    }

    return new Response("Not found", { status: 404 });
  }

  /**
   * Checks if a request is allowed under the current rate limit.
   */
  async handleCheck(): Promise<Response> {
    const now = Date.now();

    // Load persisted cooldown state
    this.cooldownUntil =
      (await this.state.storage.get("cooldownUntil")) ?? null;
    if (this.cooldownUntil && now < this.cooldownUntil) {
      return new Response("Rate limit active (cooldown)", { status: 429 });
    }

    const timestamps: number[] =
      (await this.state.storage.get("timestamps")) || [];

    // Remove old timestamps
    const relevantTimestamps = timestamps.filter(
      (ts) => now - ts < RATE_LIMIT_WINDOW
    );

    if (relevantTimestamps.length >= RATE_LIMIT) {
      return new Response("Rate limit exceeded", { status: 429 });
    }

    relevantTimestamps.push(now);
    await this.state.storage.put("timestamps", relevantTimestamps);

    return new Response(JSON.stringify({ success: true }), {
      headers: { "Content-Type": "application/json" },
    });
  }

  /**
   * Initiates a backoff period, typically after receiving a 429 from the API.
   */
  async handleInitiateBackoff(request: Request): Promise<Response> {
    const { duration = 60000 } = await request.json<{ duration?: number }>(); // Default to 60 seconds
    this.cooldownUntil = Date.now() + duration;

    // Persist cooldown state
    await this.state.storage.put("cooldownUntil", this.cooldownUntil);

    return new Response(
      JSON.stringify({ success: true, cooldownUntil: this.cooldownUntil }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
