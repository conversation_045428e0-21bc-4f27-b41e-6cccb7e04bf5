{"version": "6", "dialect": "sqlite", "id": "61d73fc4-67e5-451b-aa9e-27884aa50312", "prevId": "d4f267b0-ae21-4a3e-9f50-9039ade98f01", "tables": {"api_keys": {"name": "api_keys", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "feed_id": {"name": "feed_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "key_preview": {"name": "key_preview", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_used_at": {"name": "last_used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"apikey_feed_idx": {"name": "apikey_feed_idx", "columns": ["feed_id"], "isUnique": false}}, "foreignKeys": {"api_keys_feed_id_feeds_id_fk": {"name": "api_keys_feed_id_feeds_id_fk", "tableFrom": "api_keys", "tableTo": "feeds", "columnsFrom": ["feed_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_keys_created_by_users_id_fk": {"name": "api_keys_created_by_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "feed_plattform_connections": {"name": "feed_plattform_connections", "columns": {"feed_id": {"name": "feed_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform_connection_id": {"name": "platform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"feed_plattform_connections_feed_id_feeds_id_fk": {"name": "feed_plattform_connections_feed_id_feeds_id_fk", "tableFrom": "feed_plattform_connections", "tableTo": "feeds", "columnsFrom": ["feed_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "feed_plattform_connections_platform_connection_id_platform_connections_id_fk": {"name": "feed_plattform_connections_platform_connection_id_platform_connections_id_fk", "tableFrom": "feed_plattform_connections", "tableTo": "platform_connections", "columnsFrom": ["platform_connection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"feed_plattform_connections_feed_id_platform_connection_id_pk": {"columns": ["feed_id", "platform_connection_id"], "name": "feed_plattform_connections_feed_id_platform_connection_id_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "feeds": {"name": "feeds", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "last_activity": {"name": "last_activity", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"feed_project_idx": {"name": "feed_project_idx", "columns": ["project_id"], "isUnique": false}, "feed_is_active_idx": {"name": "feed_is_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"feeds_project_id_projects_id_fk": {"name": "feeds_project_id_projects_id_fk", "tableFrom": "feeds", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "generated_links": {"name": "generated_links", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "plattform_connection_id": {"name": "plattform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "redirect_url": {"name": "redirect_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {"generated_links_token_unique": {"name": "generated_links_token_unique", "columns": ["token"], "isUnique": true}, "generated_link_plattform_connection_idx": {"name": "generated_link_plattform_connection_idx", "columns": ["plattform_connection_id"], "isUnique": false}, "generated_link_expires_at_idx": {"name": "generated_link_expires_at_idx", "columns": ["expires_at"], "isUnique": false}, "generated_link_is_active_idx": {"name": "generated_link_is_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"generated_links_plattform_connection_id_platform_connections_id_fk": {"name": "generated_links_plattform_connection_id_platform_connections_id_fk", "tableFrom": "generated_links", "tableTo": "platform_connections", "columnsFrom": ["plattform_connection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "generated_links_created_by_users_id_fk": {"name": "generated_links_created_by_users_id_fk", "tableFrom": "generated_links", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organization_members": {"name": "organization_members", "columns": {"organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'member'"}, "invited_by": {"name": "invited_by", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "invited_at": {"name": "invited_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "joined_at": {"name": "joined_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {"organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_invited_by_users_id_fk": {"name": "organization_members_invited_by_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"organization_members_organization_id_user_id_pk": {"columns": ["organization_id", "user_id"], "name": "organization_members_organization_id_user_id_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "organizations": {"name": "organizations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paddle_customer_id": {"name": "paddle_customer_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paddle_subscription_id": {"name": "paddle_subscription_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "plan": {"name": "plan", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'standard'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'inactive'"}, "current_period_end": {"name": "current_period_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "billing_email": {"name": "billing_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "platform_connections": {"name": "platform_connections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform_account_id": {"name": "platform_account_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "platform_account_name": {"name": "platform_account_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_encrypted": {"name": "access_token_encrypted", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_encrypted": {"name": "refresh_token_encrypted", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scopes": {"name": "scopes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "is_connected": {"name": "is_connected", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "has_error": {"name": "has_error", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "needs_reconnect": {"name": "needs_reconnect", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "token_expires_at": {"name": "token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_checked_at": {"name": "last_checked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_polled_at": {"name": "last_polled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "subscriber_count": {"name": "subscriber_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "platform_metrics": {"name": "platform_metrics", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "locked_at": {"name": "locked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "post_limit": {"name": "post_limit", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 50}, "is_syncing": {"name": "is_syncing", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {"connection_feed_idx": {"name": "connection_feed_idx", "columns": ["project_id"], "isUnique": false}, "connection_platform_idx": {"name": "connection_platform_idx", "columns": ["platform"], "isUnique": false}, "connection_is_active_idx": {"name": "connection_is_active_idx", "columns": ["is_active"], "isUnique": false}, "connection_is_connected_idx": {"name": "connection_is_connected_idx", "columns": ["is_connected"], "isUnique": false}}, "foreignKeys": {"platform_connections_project_id_projects_id_fk": {"name": "platform_connections_project_id_projects_id_fk", "tableFrom": "platform_connections", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "posts": {"name": "posts", "columns": {"platform_connection_id": {"name": "platform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "media_id": {"name": "media_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "comments_count": {"name": "comments_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_url": {"name": "media_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_type": {"name": "media_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "permalink": {"name": "permalink", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_webhook_update": {"name": "last_webhook_update", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_fetched": {"name": "last_fetched", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"connection_timestamp_idx": {"name": "connection_timestamp_idx", "columns": ["platform_connection_id", "timestamp"], "isUnique": false}, "post_media_type_idx": {"name": "post_media_type_idx", "columns": ["media_type"], "isUnique": false}}, "foreignKeys": {"posts_platform_connection_id_platform_connections_id_fk": {"name": "posts_platform_connection_id_platform_connections_id_fk", "tableFrom": "posts", "tableTo": "platform_connections", "columnsFrom": ["platform_connection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"posts_platform_connection_id_media_id_pk": {"columns": ["platform_connection_id", "media_id"], "name": "posts_platform_connection_id_media_id_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "projects": {"name": "projects", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {"project_organization_idx": {"name": "project_organization_idx", "columns": ["organization_id"], "isUnique": false}, "project_is_active_idx": {"name": "project_is_active_idx", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"projects_organization_id_organizations_id_fk": {"name": "projects_organization_id_organizations_id_fk", "tableFrom": "projects", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_user_id_users_id_fk": {"name": "projects_user_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tiktok_polling_queue": {"name": "tiktok_polling_queue", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "platform_connection_id": {"name": "platform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "last_attempted_at": {"name": "last_attempted_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {"tpq_platform_connection_idx": {"name": "tpq_platform_connection_idx", "columns": ["platform_connection_id"], "isUnique": false}, "tpq_status_idx": {"name": "tpq_status_idx", "columns": ["status"], "isUnique": false}}, "foreignKeys": {"tiktok_polling_queue_platform_connection_id_platform_connections_id_fk": {"name": "tiktok_polling_queue_platform_connection_id_platform_connections_id_fk", "tableFrom": "tiktok_polling_queue", "tableTo": "platform_connections", "columnsFrom": ["platform_connection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "webhook_events": {"name": "webhook_events", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "event_id": {"name": "event_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payload_hash": {"name": "payload_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_at": {"name": "received_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "processed": {"name": "processed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}}, "indexes": {"webhook_events_platform_event_idx": {"name": "webhook_events_platform_event_idx", "columns": ["platform", "event_id"], "isUnique": false}, "webhook_events_payload_hash_idx": {"name": "webhook_events_payload_hash_idx", "columns": ["payload_hash"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}