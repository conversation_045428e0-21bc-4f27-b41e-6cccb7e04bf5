{"version": "6", "dialect": "sqlite", "tables": {"api_keys": {"name": "api_keys", "columns": {"key": {"name": "key", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "feed_id": {"name": "feed_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_used_at": {"name": "last_used_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"apikey_feed_idx": {"name": "apikey_feed_idx", "columns": ["feed_id"], "isUnique": true}}, "foreignKeys": {"api_keys_feed_id_feeds_id_fk": {"name": "api_keys_feed_id_feeds_id_fk", "tableFrom": "api_keys", "columnsFrom": ["feed_id"], "tableTo": "feeds", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "platform_connection_id": {"name": "platform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment_id": {"name": "comment_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "media_id": {"name": "media_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"platform_comment_idx": {"name": "platform_comment_idx", "columns": ["platform_connection_id", "comment_id"], "isUnique": true}, "connection_media_comment_idx": {"name": "connection_media_comment_idx", "columns": ["platform_connection_id", "media_id", "timestamp"], "isUnique": true}}, "foreignKeys": {"comments_platform_connection_id_platform_connections_id_fk": {"name": "comments_platform_connection_id_platform_connections_id_fk", "tableFrom": "comments", "columnsFrom": ["platform_connection_id"], "tableTo": "platform_connections", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "feeds": {"name": "feeds", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {}, "foreignKeys": {"feeds_user_id_users_id_fk": {"name": "feeds_user_id_users_id_fk", "tableFrom": "feeds", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "platform_connections": {"name": "platform_connections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "feed_id": {"name": "feed_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform": {"name": "platform", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform_account_id": {"name": "platform_account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "platform_account_name": {"name": "platform_account_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "api_type": {"name": "api_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'graph'"}, "access_token_encrypted": {"name": "access_token_encrypted", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_encrypted": {"name": "refresh_token_encrypted", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scopes": {"name": "scopes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}, "token_expires_at": {"name": "token_expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_checked_at": {"name": "last_checked_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_polled_at": {"name": "last_polled_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"platform_account_idx": {"name": "platform_account_idx", "columns": ["platform", "platform_account_id"], "isUnique": true}, "connection_feed_idx": {"name": "connection_feed_idx", "columns": ["feed_id"], "isUnique": true}}, "foreignKeys": {"platform_connections_feed_id_feeds_id_fk": {"name": "platform_connections_feed_id_feeds_id_fk", "tableFrom": "platform_connections", "columnsFrom": ["feed_id"], "tableTo": "feeds", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "posts": {"name": "posts", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "platform_connection_id": {"name": "platform_connection_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "media_id": {"name": "media_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "comments_count": {"name": "comments_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_url": {"name": "media_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "media_type": {"name": "media_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "permalink": {"name": "permalink", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_webhook_update": {"name": "last_webhook_update", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_fetched": {"name": "last_fetched", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"connection_media_idx": {"name": "connection_media_idx", "columns": ["platform_connection_id", "media_id"], "isUnique": true}, "connection_timestamp_idx": {"name": "connection_timestamp_idx", "columns": ["platform_connection_id", "timestamp"], "isUnique": true}}, "foreignKeys": {"posts_platform_connection_id_platform_connections_id_fk": {"name": "posts_platform_connection_id_platform_connections_id_fk", "tableFrom": "posts", "columnsFrom": ["platform_connection_id"], "tableTo": "platform_connections", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch('now', 'subsec') * 1000)"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "enums": {}, "_meta": {"tables": {}, "columns": {}}, "id": "820f75e1-9210-44f2-8208-12aba592a3c9", "prevId": "00000000-0000-0000-0000-000000000000", "views": {}}