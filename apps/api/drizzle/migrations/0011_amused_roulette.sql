ALTER TABLE `api_keys` ADD `is_active` integer DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE `api_keys` DROP COLUMN `status`;--> statement-breakpoint
ALTER TABLE `feeds` ADD `is_active` integer DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE `feeds` DROP COLUMN `status`;--> statement-breakpoint
ALTER TABLE `platform_connections` ADD `is_active` integer DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE `platform_connections` ADD `is_connected` integer DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE `platform_connections` ADD `has_error` integer DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE `platform_connections` DROP COLUMN `status`;--> statement-breakpoint
ALTER TABLE `projects` ADD `is_active` integer DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE `projects` DROP COLUMN `status`;