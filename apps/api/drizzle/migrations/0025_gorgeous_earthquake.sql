PRAGMA foreign_keys=OFF;--> statement-breakpoint

-- Step 1: Create new posts table with UUID primary key
CREATE TABLE `__new_posts` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`media_id` text NOT NULL,
	`platform` text NOT NULL,
	`like_count` integer DEFAULT 0,
	`comments_count` integer DEFAULT 0,
	`caption` text,
	`media_url` text,
	`media_type` text,
	`permalink` text,
	`timestamp` integer,
	`last_webhook_update` integer,
	`last_fetched` integer,
	`engagement` text,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint

-- Step 2: Insert data from old posts table with generated UUIDs
INSERT INTO `__new_posts`("id", "platform_connection_id", "media_id", "platform", "like_count", "comments_count", "caption", "media_url", "media_type", "permalink", "timestamp", "last_webhook_update", "last_fetched", "engagement")
SELECT
	'POST-' || substr(hex(randomblob(16)), 1, 16) as id,
	"platform_connection_id",
	"media_id",
	"platform",
	"like_count",
	"comments_count",
	"caption",
	"media_url",
	"media_type",
	"permalink",
	"timestamp",
	"last_webhook_update",
	"last_fetched",
	"engagement"
FROM `posts`;
--> statement-breakpoint

-- Step 3: Create new post_media_items table that references the new posts.id
CREATE TABLE `__new_post_media_items` (
	`id` text PRIMARY KEY NOT NULL,
	`post_id` text NOT NULL,
	`media_url` text NOT NULL,
	`media_type` text NOT NULL,
	`display_order` integer NOT NULL,
	FOREIGN KEY (`post_id`) REFERENCES `__new_posts`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint

-- Step 4: Insert post_media_items data, linking to new post IDs
INSERT INTO `__new_post_media_items`("id", "post_id", "media_url", "media_type", "display_order")
SELECT
	pmi.id,
	np.id as post_id,
	pmi.media_url,
	pmi.media_type,
	pmi.display_order
FROM `post_media_items` pmi
INNER JOIN `posts` p ON (pmi.post_id = p.media_id AND pmi.platform_connection_id = p.platform_connection_id)
INNER JOIN `__new_posts` np ON (np.platform_connection_id = p.platform_connection_id AND np.media_id = p.media_id);
--> statement-breakpoint

-- Step 5: Drop old tables
DROP TABLE `post_media_items`;--> statement-breakpoint
DROP TABLE `posts`;--> statement-breakpoint

-- Step 6: Rename new tables
ALTER TABLE `__new_posts` RENAME TO `posts`;--> statement-breakpoint
ALTER TABLE `__new_post_media_items` RENAME TO `post_media_items`;--> statement-breakpoint

PRAGMA foreign_keys=ON;--> statement-breakpoint

-- Step 7: Create indexes
CREATE UNIQUE INDEX `posts_connection_media_unique` ON `posts` (`platform_connection_id`,`media_id`);--> statement-breakpoint
CREATE INDEX `connection_timestamp_idx` ON `posts` (`platform_connection_id`,`timestamp`);--> statement-breakpoint
CREATE INDEX `post_media_type_idx` ON `posts` (`media_type`);--> statement-breakpoint
CREATE INDEX `pmi_post_idx` ON `post_media_items` (`post_id`);
CREATE TABLE `__new_post_media_items` (
	`id` text PRIMARY KEY NOT NULL,
	`post_id` text NOT NULL,
	`media_url` text NOT NULL,
	`media_type` text NOT NULL,
	`display_order` integer NOT NULL,
	FOREIGN KEY (`post_id`) REFERENCES `posts`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_post_media_items`("id", "post_id", "media_url", "media_type", "display_order") SELECT "id", "post_id", "media_url", "media_type", "display_order" FROM `post_media_items`;--> statement-breakpoint
DROP TABLE `post_media_items`;--> statement-breakpoint
ALTER TABLE `__new_post_media_items` RENAME TO `post_media_items`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE INDEX `pmi_post_idx` ON `post_media_items` (`post_id`);--> statement-breakpoint
CREATE TABLE `__new_posts` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`media_id` text NOT NULL,
	`platform` text NOT NULL,
	`like_count` integer DEFAULT 0,
	`comments_count` integer DEFAULT 0,
	`caption` text,
	`media_url` text,
	`media_type` text,
	`permalink` text,
	`timestamp` integer,
	`last_webhook_update` integer,
	`last_fetched` integer,
	`engagement` text,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_posts`("id", "platform_connection_id", "media_id", "platform", "like_count", "comments_count", "caption", "media_url", "media_type", "permalink", "timestamp", "last_webhook_update", "last_fetched", "engagement") SELECT "id", "platform_connection_id", "media_id", "platform", "like_count", "comments_count", "caption", "media_url", "media_type", "permalink", "timestamp", "last_webhook_update", "last_fetched", "engagement" FROM `posts`;--> statement-breakpoint
DROP TABLE `posts`;--> statement-breakpoint
ALTER TABLE `__new_posts` RENAME TO `posts`;--> statement-breakpoint
CREATE UNIQUE INDEX `posts_connection_media_unique` ON `posts` (`platform_connection_id`,`media_id`);--> statement-breakpoint
CREATE INDEX `connection_timestamp_idx` ON `posts` (`platform_connection_id`,`timestamp`);--> statement-breakpoint
CREATE INDEX `post_media_type_idx` ON `posts` (`media_type`);