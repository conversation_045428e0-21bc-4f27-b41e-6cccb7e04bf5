CREATE TABLE `comments` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`comment_id` text NOT NULL,
	`media_id` text NOT NULL,
	`text` text NOT NULL,
	`user_id` text,
	`username` text,
	`timestamp` integer,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `connection_comment_idx` ON `comments` (`platform_connection_id`,`comment_id`);