CREATE TABLE `generated_links` (
	`id` text PRIMARY KEY NOT NULL,
	`project_id` text NOT NULL,
	`platform` text,
	`token` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`redirect_url` text,
	`expires_at` integer,
	`is_active` integer DEFAULT true NOT NULL,
	`created_by` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`project_id`) REFERENCES `feeds`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `organization_members` (
	`id` text PRIMARY KEY NOT NULL,
	`organization_id` text NOT NULL,
	`user_id` text NOT NULL,
	`role` text DEFAULT 'member' NOT NULL,
	`invited_by` text,
	`invited_at` integer,
	`joined_at` integer DEFAULT (unixepoch()) NOT NULL,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`invited_by`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `organizations` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`slug` text NOT NULL,
	`description` text,
	`logo` text,
	`paddle_customer_id` text,
	`paddle_subscription_id` text,
	`plan` text DEFAULT 'standard' NOT NULL,
	`status` text DEFAULT 'inactive' NOT NULL,
	`current_period_end` integer,
	`billing_email` text,
	`created_at` integer DEFAULT (unixepoch()) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch()) NOT NULL
);
--> statement-breakpoint
DROP TABLE `comments`;--> statement-breakpoint
/*
 SQLite does not support "Dropping foreign key" out of the box, we do not generate automatic migration for that, so it has to be done manually
 Please refer to: https://www.techonthenet.com/sqlite/tables/alter_table.php
                  https://www.sqlite.org/lang_altertable.html

 Due to that we don't generate migration automatically and it has to be done manually
*/--> statement-breakpoint
ALTER TABLE api_keys ADD `created_by` text NOT NULL REFERENCES users(id);--> statement-breakpoint
ALTER TABLE api_keys ADD `key_preview` text NOT NULL;--> statement-breakpoint
ALTER TABLE api_keys ADD `updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000);--> statement-breakpoint
ALTER TABLE feeds ADD `organization_id` text NOT NULL;--> statement-breakpoint
ALTER TABLE feeds ADD `description` text;--> statement-breakpoint
ALTER TABLE feeds ADD `status` text DEFAULT 'active' NOT NULL;--> statement-breakpoint
ALTER TABLE feeds ADD `updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000);--> statement-breakpoint
ALTER TABLE users ADD `avatar` text;--> statement-breakpoint
ALTER TABLE users ADD `updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000);--> statement-breakpoint
CREATE UNIQUE INDEX `generated_links_token_unique` ON `generated_links` (`token`);--> statement-breakpoint
CREATE UNIQUE INDEX `organizations_slug_unique` ON `organizations` (`slug`);--> statement-breakpoint
/*
 SQLite does not support "Creating foreign key on existing column" out of the box, we do not generate automatic migration for that, so it has to be done manually
 Please refer to: https://www.techonthenet.com/sqlite/tables/alter_table.php
                  https://www.sqlite.org/lang_altertable.html

 Due to that we don't generate migration automatically and it has to be done manually
*/--> statement-breakpoint
ALTER TABLE `platform_connections` DROP COLUMN `api_type`;