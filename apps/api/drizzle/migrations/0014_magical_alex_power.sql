CREATE TABLE `tiktok_polling_queue` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`status` text DEFAULT 'pending' NOT NULL,
	`attempts` integer DEFAULT 0 NOT NULL,
	`last_attempted_at` integer,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	`updated_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000) NOT NULL,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `tpq_platform_connection_idx` ON `tiktok_polling_queue` (`platform_connection_id`);--> statement-breakpoint
CREATE INDEX `tpq_status_idx` ON `tiktok_polling_queue` (`status`);--> statement-breakpoint
CREATE TABLE `webhook_events` (
	`id` text PRIMARY KEY NOT NULL,
	`platform` text NOT NULL,
	`event_id` text NOT NULL,
	`payload_hash` text NOT NULL,
	`received_at` integer NOT NULL,
	`processed` integer DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE INDEX `webhook_events_platform_event_idx` ON `webhook_events` (`platform`,`event_id`);--> statement-breakpoint
CREATE INDEX `webhook_events_payload_hash_idx` ON `webhook_events` (`payload_hash`);--> statement-breakpoint
ALTER TABLE `platform_connections` ADD `locked_at` integer;--> statement-breakpoint
CREATE INDEX `connection_platform_idx` ON `platform_connections` (`platform`);--> statement-breakpoint
CREATE INDEX `connection_is_active_idx` ON `platform_connections` (`is_active`);--> statement-breakpoint
CREATE INDEX `connection_is_connected_idx` ON `platform_connections` (`is_connected`);--> statement-breakpoint
CREATE INDEX `feed_is_active_idx` ON `feeds` (`is_active`);--> statement-breakpoint
CREATE INDEX `post_media_type_idx` ON `posts` (`media_type`);--> statement-breakpoint
CREATE INDEX `project_is_active_idx` ON `projects` (`is_active`);