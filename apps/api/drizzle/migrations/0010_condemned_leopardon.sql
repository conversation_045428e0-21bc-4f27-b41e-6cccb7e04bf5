CREATE TABLE `feed_plattform_connections` (
	`feed_id` text NOT NULL,
	`platform_connection_id` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	PRIMARY KEY(`feed_id`, `platform_connection_id`),
	FOREIGN KEY (`feed_id`) REFERENCES `feeds`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_platform_connections` (
	`id` text PRIMARY KEY NOT NULL,
	`project_id` text NOT NULL,
	`platform` text NOT NULL,
	`name` text NOT NULL,
	`platform_account_id` text,
	`platform_account_name` text,
	`access_token_encrypted` text,
	`refresh_token_encrypted` text,
	`scopes` text,
	`status` text DEFAULT 'auth_needed' NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	`token_expires_at` integer,
	`last_checked_at` integer,
	`last_polled_at` integer,
	FOREIGN KEY (`project_id`) REFERENCES `projects`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_platform_connections`("id", "project_id", "platform", "name", "platform_account_id", "platform_account_name", "access_token_encrypted", "refresh_token_encrypted", "scopes", "status", "created_at", "token_expires_at", "last_checked_at", "last_polled_at") SELECT "id", "project_id", "platform", "name", "platform_account_id", "platform_account_name", "access_token_encrypted", "refresh_token_encrypted", "scopes", "status", "created_at", "token_expires_at", "last_checked_at", "last_polled_at" FROM `platform_connections`;--> statement-breakpoint
DROP TABLE `platform_connections`;--> statement-breakpoint
ALTER TABLE `__new_platform_connections` RENAME TO `platform_connections`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE INDEX `connection_feed_idx` ON `platform_connections` (`project_id`);