CREATE TABLE `post_media_items` (
	`id` text PRIMARY KEY NOT NULL,
	`post_id` text NOT NULL,
	`platform_connection_id` text NOT NULL,
	`media_url` text NOT NULL,
	`media_type` text NOT NULL,
	`display_order` integer NOT NULL,
	FOREIGN KEY (`post_id`) REFERENCES `posts`(`media_id`) ON UPDATE no action ON DELETE cascade,
	FOREI<PERSON>N KEY (`platform_connection_id`) REFERENCES `posts`(`platform_connection_id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE INDEX `pmi_post_idx` ON `post_media_items` (`post_id`,`platform_connection_id`);--> statement-breakpoint
ALTER TABLE `posts` ADD `platform` text NOT NULL;--> statement-breakpoint
ALTER TABLE `posts` ADD `engagement` text;--> statement-breakpoint
ALTER TABLE `platform_connections` DROP COLUMN `auth_type`;