CREATE TABLE `api_keys` (
	`key` text PRIMARY KEY NOT NULL,
	`feed_id` text NOT NULL,
	`description` text,
	`status` text DEFAULT 'active',
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	`expires_at` integer,
	`last_used_at` integer,
	FOREIGN KEY (`feed_id`) REFERENCES `feeds`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `comments` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`comment_id` text NOT NULL,
	`media_id` text NOT NULL,
	`user_id` text,
	`username` text,
	`text` text,
	`timestamp` integer,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `feeds` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` text NOT NULL,
	`name` text NOT NULL,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `platform_connections` (
	`id` text PRIMARY KEY NOT NULL,
	`feed_id` text NOT NULL,
	`platform` text NOT NULL,
	`platform_account_id` text NOT NULL,
	`platform_account_name` text,
	`api_type` text DEFAULT 'graph' NOT NULL,
	`access_token_encrypted` text,
	`refresh_token_encrypted` text,
	`scopes` text,
	`status` text DEFAULT 'active',
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000),
	`token_expires_at` integer,
	`last_checked_at` integer,
	`last_polled_at` integer,
	FOREIGN KEY (`feed_id`) REFERENCES `feeds`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `posts` (
	`id` text PRIMARY KEY NOT NULL,
	`platform_connection_id` text NOT NULL,
	`media_id` text NOT NULL,
	`like_count` integer DEFAULT 0,
	`comments_count` integer DEFAULT 0,
	`caption` text,
	`media_url` text,
	`media_type` text,
	`permalink` text,
	`timestamp` integer,
	`last_webhook_update` integer,
	`last_fetched` integer,
	FOREIGN KEY (`platform_connection_id`) REFERENCES `platform_connections`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` text PRIMARY KEY NOT NULL,
	`email` text,
	`name` text,
	`created_at` integer DEFAULT (unixepoch('now', 'subsec') * 1000)
);
--> statement-breakpoint
CREATE UNIQUE INDEX `apikey_feed_idx` ON `api_keys` (`feed_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `platform_comment_idx` ON `comments` (`platform_connection_id`,`comment_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `connection_media_comment_idx` ON `comments` (`platform_connection_id`,`media_id`,`timestamp`);--> statement-breakpoint
CREATE UNIQUE INDEX `platform_account_idx` ON `platform_connections` (`platform`,`platform_account_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `connection_feed_idx` ON `platform_connections` (`feed_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `connection_media_idx` ON `posts` (`platform_connection_id`,`media_id`);--> statement-breakpoint
CREATE UNIQUE INDEX `connection_timestamp_idx` ON `posts` (`platform_connection_id`,`timestamp`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);