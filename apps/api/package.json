{"name": "@socialfeed/api", "version": "0.2.0", "private": true, "scripts": {"dev": "wrangler dev src/index.ts", "deploy": "wrangler deploy src/index.ts", "db:generate": "drizzle-kit generate", "db:migrate:local": "wrangler d1 migrations apply socialfeed-test-2 --local", "db:migrate:prod": "wrangler d1 migrations apply socialfeed-test-2 --remote", "logs": "wrangler tail", "check-types": "tsc --noEmit"}, "dependencies": {"@hono/zod-validator": "^0.7.1", "@noble/ciphers": "^1.3.0", "@noble/hashes": "^1.8.0", "@propelauth/node": "^2.1.33", "@socialfeed/drizzle-schema": "workspace:*", "@socialfeed/utils": "workspace:*", "crypto-js": "^4.2.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "hono": "^4.7.7", "jose": "^5.10.0", "luxon": "^3.7.1", "zod": "^3.25.67"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250618.0", "@types/crypto-js": "^4.2.2", "@types/luxon": "^3.6.2", "@types/node": "^20.17.31", "drizzle-kit": "^0.31.1", "typescript": "^5.8.3", "wrangler": "^4.31.0"}, "module": "src/index.ts"}