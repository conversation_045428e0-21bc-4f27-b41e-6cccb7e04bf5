{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "deploy": {"dependsOn": ["^deploy"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}, "db:migrate:local": {"cache": false, "persistent": true}, "db:migrate:prod": {"cache": false, "persistent": true}}}